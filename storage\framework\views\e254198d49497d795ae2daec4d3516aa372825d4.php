<?php if (isset($component)) { $__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0 = $component; } ?>
<?php $component = App\View\Components\MdModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('md-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\MdModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
	 <?php $__env->slot('class', null, []); ?> frontend-modal-login <?php $__env->endSlot(); ?>

  <div class="lezada-form p-4 bg-white">
    <form action="/login" method="POST">
      <?php echo csrf_field(); ?>
      <div class="row">
        <div class="col-lg-12">
          <!--=======  login title  =======-->

          <div class="section-title section-title--login text-center mb-50">
            <img src="<?php echo e(asset('assets/svg/logo/Trust - Badge dark Outline on Light BG.svg')); ?>" alt="" height="100">
            <p class="mt-2">Sign in to continue</p>

            <?php if(session('error') ): ?>
              <p class="text-danger"><?php echo e(session('error')); ?></p>
             <?php endif; ?>

            
            <?php if($errors->any()): ?>
                 <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <p class="text-danger"><?php echo e($error); ?></p>
                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
             <?php endif; ?> 
          </div>

          <!--=======  End of login title  =======-->
        </div>
        <div class="col-lg-12 mb-20">
          Username
          <input type="text" name="username" placeholder="Phone or Name or Email address" required>
        </div>
        <div class="col-lg-12 mb-30">
          Password
          <input type="password" name="password" placeholder="Enter Password" required>
        </div>


        <div class="col-lg-12 row mb-20">
          <div class="col-md-6"> <input type="checkbox"> <span class="remember-text">Remember me</span></div>
          <div class="col-md-6"><a href="#"  class="reg-modal float-right">Register</a></div>
          <!-- <div class="col-md-6"><a href="/password/reset" class="float-right">Lost your password?</a></div> -->
        </div>
        

        <div class="col-lg-12 text-center mb-20">
          <button class="lezada-button lezada-button--medium">LOGIN</button>
        </div>

        
      </div>
    </form>
  </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0)): ?>
<?php $component = $__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0; ?>
<?php unset($__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
  <script type="text/javascript">
    $('document').ready(function() {
      $('.reg-modal').click(function() {
        $('.modal').modal('hide');
        $('.frontend-modal-register').modal('show');
      });
    });
  </script>
<?php $__env->stopPush(); ?>





<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/frontend-login-modal.blade.php ENDPATH**/ ?>