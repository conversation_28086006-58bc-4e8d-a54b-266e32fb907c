@extends('layouts.modernized-admin')

@section('title', 'Edit Auction Category - Vertigo AMS')

@section('page-title', 'Edit Auction Category')
@section('page-subtitle', 'Update the details of this auction category.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('auction-types.show', $auctionType) }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
        <span class="hidden lg:inline">View Category</span>
        <span class="lg:hidden">View</span>
    </a>
    <a href="{{ route('auction-types.index') }}" class="flex items-center bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        <span class="hidden lg:inline">Back to Categories</span>
        <span class="lg:hidden">Back</span>
    </a>
</div>
@endsection

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Form Card -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Category Details</h3>
            <p class="text-sm text-gray-600 mt-1">Update the information below to modify this auction category</p>
        </div>
        
        <form method="POST" action="{{ route('auction-types.update', $auctionType) }}" enctype="multipart/form-data" class="p-6">
            @csrf
            @method('PUT')
            
            <!-- Category Name -->
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Name <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       value="{{ old('name', $auctionType->name) }}" 
                       required
                       maxlength="255"
                       placeholder="Enter category name"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('name') border-red-300 @enderror">
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Category Type -->
            <div class="mb-6">
                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Type <span class="text-red-500">*</span>
                </label>
                <select id="type" 
                        name="type" 
                        required
                        class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('type') border-red-300 @enderror">
                    <option value="">Select category type...</option>
                    <option value="online" {{ old('type', $auctionType->type) == 'online' ? 'selected' : '' }}>Online</option>
                    <option value="live" {{ old('type', $auctionType->type) == 'live' ? 'selected' : '' }}>Live</option>
                    <option value="cash" {{ old('type', $auctionType->type) == 'cash' ? 'selected' : '' }}>Cash Sale</option>
                </select>
                @error('type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Choose the type of auction this category will be used for</p>
            </div>

            <!-- Description -->
            <div class="mb-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4"
                          maxlength="255"
                          placeholder="Enter a description for this category (optional)"
                          class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('description') border-red-300 @enderror">{{ old('description', $auctionType->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Provide additional details about this category</p>
            </div>

            <!-- Select Products -->
            <div class="mb-6">
                <label for="items" class="block text-sm font-medium text-gray-700 mb-2">
                    Select Products
                </label>
                <div class="tom-select-custom">
                    <select id="items"
                            name="items[]"
                            multiple
                            class="js-select form-select"
                            data-hs-tom-select-options='{
                                "placeholder": "Search and select products...",
                                "maxItems": null
                            }'>
                        @php $selectedItems = old('items', $auctionType->items->pluck('id')->toArray()) @endphp
                        @foreach($items as $item)
                        <option value="{{ $item->id }}"
                                {{ in_array($item->id, $selectedItems) ? 'selected' : '' }}
                                data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="{{ $item->image ?? asset('assets/img/placeholder.jpg') }}" alt="{{ $item->name }}" /><span class="text-truncate">{{ $item->name }}</span></span>'>
                            {{ $item->name }}
                        </option>
                        @endforeach
                    </select>
                </div>
                @error('items')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Search and select products to include in this category</p>
            </div>

            <!-- Current Images -->
            @php $images = $auctionType->getMedia('media') @endphp
            @if($images->count() > 0)
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Current Images
                </label>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                    @foreach($images as $image)
                    <div class="relative group">
                        <img src="{{ $image->getUrl() }}" alt="Category image" class="w-full h-24 object-cover rounded-lg border border-gray-200">
                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                            <button type="button" class="text-white hover:text-red-300 transition-colors duration-200" onclick="removeImage({{ $image->id }})">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Image Upload Section -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Add New Images
                </label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors duration-200">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <div class="mt-4">
                        <label for="media" class="cursor-pointer">
                            <span class="mt-2 block text-sm font-medium text-gray-900">
                                Upload additional images
                            </span>
                            <span class="mt-1 block text-sm text-gray-500">
                                PNG, JPG, GIF up to 10MB each
                            </span>
                        </label>
                        <input id="media" name="media[]" type="file" multiple accept="image/*" class="sr-only">
                    </div>
                </div>
                @error('media')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('auction-types.show', $auctionType) }}" 
                   class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-primary-500 to-primary-600 border border-transparent rounded-lg hover:from-primary-600 hover:to-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <svg class="h-4 w-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Category
                </button>
            </div>
        </form>
    </div>

    <!-- Category Info -->
    <div class="mt-8 bg-gray-50 rounded-xl p-6 border border-gray-200">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-800">Category Information</h3>
                <div class="mt-2 text-sm text-gray-600">
                    <p><strong>Created:</strong> {{ $auctionType->created_at->format('M j, Y \a\t g:i A') }}</p>
                    <p><strong>Last Updated:</strong> {{ $auctionType->updated_at->format('M j, Y \a\t g:i A') }}</p>
                    <p><strong>Created By:</strong> {{ optional($auctionType->createdBy)->name ?? 'Unknown' }}</p>
                    <p><strong>Current Items:</strong> {{ $auctionType->items()->whereNull('closed_by')->count() }} active items</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File upload preview
    const fileInput = document.getElementById('media');
    const uploadArea = fileInput.closest('.border-dashed');
    
    fileInput.addEventListener('change', function(e) {
        const files = e.target.files;
        if (files.length > 0) {
            uploadArea.classList.add('border-primary-300', 'bg-primary-50');
            uploadArea.classList.remove('border-gray-300');
            
            const fileNames = Array.from(files).map(file => file.name).join(', ');
            const existingText = uploadArea.querySelector('.text-gray-500');
            existingText.textContent = `Selected: ${fileNames}`;
        }
    });

    // Tom Select will handle the items dropdown automatically
    
    console.log('Modernized Edit Auction Category page loaded');
});

function removeImage(imageId) {
    if (confirm('Are you sure you want to remove this image?')) {
        // This would typically make an AJAX call to remove the image
        // For now, we'll just hide the image element
        event.target.closest('.relative').style.display = 'none';
        
        // In a real implementation, you'd make an AJAX call here:
        // fetch(`/admin/auction-types/images/${imageId}`, { method: 'DELETE' })
    }
}
</script>
@endpush
