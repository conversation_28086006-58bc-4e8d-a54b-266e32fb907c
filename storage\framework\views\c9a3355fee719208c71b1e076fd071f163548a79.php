<?php $__env->startSection('title', 'Auctions - Vertigo AMS'); ?>

<?php $__env->startSection('page-title', 'Bid Management'); ?>
<?php $__env->startSection('page-subtitle', 'Manage auction bids and track bidding activity across all auctions.'); ?>

<?php $__env->startSection('quick-actions'); ?>
<div class="flex space-x-2">
    <a href="<?php echo e(route('auctions.create')); ?>" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span class="hidden lg:inline">Add Bid</span>
        <span class="lg:hidden">Add</span>
    </a>
    <a href="<?php echo e(route('auction-listing.create')); ?>" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
        <span class="hidden lg:inline">Create Auction</span>
        <span class="lg:hidden">Create</span>
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Enhanced Filters Section -->
<div class="bg-white rounded-xl p-6 mb-6 shadow-sm border border-gray-100">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Filter Bids</h3>
            <p class="text-sm text-gray-600">Use filters to find specific bids and auctions</p>
        </div>
        
        <form id="filter" class="flex flex-col sm:flex-row gap-3" method="GET">
            <!-- Item Filter -->
            <div class="min-w-0 flex-1 sm:min-w-[200px]">
                <label class="block text-xs font-medium text-gray-700 mb-1">Filter by Item</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200" 
                        name="item_id" onchange="this.form.submit()" autocomplete="off">
                    <option value="0">All Items</option>
                    <?php $__currentLoopData = App\Models\Item::whereNull('closed_by')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option <?php if(request()->item_id == $item->id): ?> selected <?php endif; ?> value="<?php echo e($item->id); ?>">
                        <?php echo e(Str::limit($item->name ?? '', 30)); ?>

                    </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <!-- Status Filter -->
            <div class="min-w-0 flex-1 sm:min-w-[150px]">
                <label class="block text-xs font-medium text-gray-700 mb-1">Filter by Status</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200" 
                        name="status" onchange="this.form.submit()" autocomplete="off">
                    <option value="0">All Status</option>
                    <?php $__currentLoopData = ['open', 'closed']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option <?php if(request()->status == $status): ?> selected <?php endif; ?> value="<?php echo e($status); ?>">
                        <?php echo e(ucfirst($status)); ?> Bids
                    </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <!-- Search Input -->
            <div class="min-w-0 flex-1 sm:min-w-[200px]">
                <label class="block text-xs font-medium text-gray-700 mb-1">Search</label>
                <div class="relative">
                    <input type="text" name="search" value="<?php echo e(request()->search); ?>" 
                           placeholder="Search bids..." 
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200">
                    <svg class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- Clear Filters Button -->
            <?php if(request()->item_id || request()->status || request()->search): ?>
            <div class="flex items-end">
                <a href="<?php echo e(route('auctions.index')); ?>"
                   class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200 flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear
                </a>
            </div>
            <?php endif; ?>
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total Results</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo e(number_format($auctions->total())); ?></p>
                <p class="text-xs text-gray-500 mt-1"><?php echo e($auctions->count()); ?> on this page</p>
            </div>
            <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Open Bids</p>
                <p class="text-2xl font-bold text-green-600"><?php echo e(number_format($auctions->where('closed_by', null)->count())); ?></p>
                <p class="text-xs text-gray-500 mt-1">On this page</p>
            </div>
            <div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Accepted Bids</p>
                <p class="text-2xl font-bold text-orange-600"><?php echo e(number_format($auctions->where('closed_by', '!=', null)->count())); ?></p>
                <p class="text-xs text-gray-500 mt-1">On this page</p>
            </div>
            <div class="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Page Value</p>
                <p class="text-2xl font-bold text-purple-600"><?php echo e(_money($auctions->sum('bid_amount'))); ?></p>
                <p class="text-xs text-gray-500 mt-1">Current page total</p>
            </div>
            <div class="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Auctions Table -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Auction Bids</h3>
                <p class="text-sm text-gray-600 mt-1">
                    <?php if(request()->search || request()->item_id || request()->status): ?>
                        Showing filtered results
                        <?php if(request()->search): ?> for "<?php echo e(request()->search); ?>" <?php endif; ?>
                    <?php else: ?>
                        Showing all auction bids
                    <?php endif; ?>
                </p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500"><?php echo e($auctions->count()); ?> bids</span>
            </div>
        </div>
    </div>

    <?php if($auctions->count() > 0): ?>
    <!-- Export Options -->
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modernized.table-export','data' => ['tableId' => 'js-datatable']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('modernized.table-export'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['table-id' => 'js-datatable']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    </div>

    <div class="overflow-x-auto">
        <table class="table js-datatable w-full">
            <thead class="bg-gray-50 border-b border-gray-200">
                <tr>
                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bidder</th>
                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auction Type</th>
                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Bid Amount</th>
                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php $__currentLoopData = $auctions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $auction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="hover:bg-gray-50 transition-colors duration-200 <?php echo e($auction->item->closed_by ? 'bg-yellow-50' : ''); ?>">
                    <!-- Item Column -->
                    <td class="px-6 py-4">
                        <?php if($auction->item): ?>
                            <?php $item = $auction->item ?>
                            <a href="<?php echo e(route('auctions.show', $auction)); ?>" class="flex items-center group">
                                <div class="flex-shrink-0 mr-4">
                                    <div class="h-16 w-16 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                                        <?php if($item->image): ?>
                                            <img class="h-full w-full object-cover group-hover:scale-105 transition-transform duration-200"
                                                 src="<?php echo e($item->image); ?>"
                                                 alt="<?php echo e($item->name); ?>"
                                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTI4IDI4TDM2IDM2TDQ0IDI4IiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo='">
                                        <?php else: ?>
                                            <div class="h-full w-full flex items-center justify-center">
                                                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
                                        <?php echo e(Str::limit($item->name ?? 'Unnamed Item', 40)); ?>

                                    </p>
                                    <?php if($item->closed_by): ?>
                                        <p class="text-xs text-yellow-600 mt-1 flex items-center">
                                            <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                            </svg>
                                            Item Closed
                                        </p>
                                    <?php endif; ?>
                                </div>
                            </a>
                        <?php else: ?>
                            <span class="text-sm text-gray-500">No item</span>
                        <?php endif; ?>
                    </td>

                    <!-- Bidder Column -->
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="h-8 w-8 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center mr-3">
                                <span class="text-xs font-medium text-white">
                                    <?php echo e(strtoupper(substr($auction->user->name ?? 'U', 0, 1))); ?>

                                </span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900"><?php echo e($auction->user->name ?? 'Unknown'); ?></p>
                                <p class="text-xs text-gray-500"><?php echo e($auction->user->email ?? ''); ?></p>
                            </div>
                        </div>
                    </td>

                    <!-- Auction Type Column -->
                    <td class="px-6 py-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            <?php echo e(optional($auction->auctionType)->type === 'live' ? 'bg-red-100 text-red-800' :
                               (optional($auction->auctionType)->type === 'online' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')); ?>">
                            <?php echo e(optional($auction->auctionType)->name ?? 'N/A'); ?>

                        </span>
                    </td>

                    <!-- Bid Amount Column -->
                    <td class="px-6 py-4 text-right">
                        <div class="text-sm font-semibold text-gray-900"><?php echo e(_money($auction->bid_amount)); ?></div>
                        <?php if($auction->initial_payment > 0): ?>
                            <div class="text-xs text-green-600"><?php echo e(_money($auction->initial_payment)); ?> paid</div>
                        <?php endif; ?>
                    </td>

                    <!-- Description Column -->
                    <td class="px-6 py-4">
                        <p class="text-sm text-gray-600"><?php echo e(Str::limit($auction->description ?? 'No description', 50)); ?></p>
                    </td>

                    <!-- Actions Column -->
                    <td class="px-6 py-4">
                        <div class="flex items-center justify-center space-x-2">
                            <?php if($auction->item->closed_by): ?>
                                <?php if($auction->closed_by && !$auction->tagged_by): ?>
                                    <button onclick="payAuctionApp.getAuction('<?php echo e($auction->id); ?>')"
                                            data-bs-toggle="modal" data-bs-target=".pay-bid-modal"
                                            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                        Add Payment
                                    </button>
                                <?php endif; ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Accepted
                                </span>
                            <?php else: ?>
                                <div class="flex items-center space-x-1">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $auction)): ?>
                                    <button onclick="acceptBid(<?php echo e($auction->id); ?>, '<?php echo e($auction->user->name ?? 'Unknown'); ?>', '<?php echo e($auction->item->name ?? 'Unknown Item'); ?>')"
                                            class="accept-bid-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="btn-text">Accept</span>
                                        <svg class="h-3 w-3 ml-1 hidden loading-spinner animate-spin" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $auction)): ?>
                                    <a href="<?php echo e(route('auctions.show', $auction)); ?>"
                                       class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200">
                                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $auction)): ?>
                                    <form action="<?php echo e(route('auctions.destroy', $auction)); ?>" method="POST"
                                          onsubmit="return confirm('Are you sure you want to cancel this bid?')" class="inline">
                                        <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                        <button type="submit"
                                                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200">
                                            <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Cancel
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if($auctions->hasPages()): ?>
    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <div class="flex items-center text-sm text-gray-700">
                <span>Showing <?php echo e($auctions->firstItem()); ?> to <?php echo e($auctions->lastItem()); ?> of <?php echo e($auctions->total()); ?> results</span>
            </div>
            <div class="flex items-center space-x-2">
                
                <?php if($auctions->onFirstPage()): ?>
                    <span class="px-3 py-2 text-sm font-medium text-gray-400 bg-white border border-gray-300 rounded-lg cursor-not-allowed">
                        Previous
                    </span>
                <?php else: ?>
                    <a href="<?php echo e($auctions->previousPageUrl()); ?>"
                       class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        Previous
                    </a>
                <?php endif; ?>

                
                <?php $__currentLoopData = $auctions->getUrlRange(1, $auctions->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($page == $auctions->currentPage()): ?>
                        <span class="px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-primary-500 to-primary-600 border border-primary-500 rounded-lg">
                            <?php echo e($page); ?>

                        </span>
                    <?php else: ?>
                        <a href="<?php echo e($url); ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <?php echo e($page); ?>

                        </a>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                
                <?php if($auctions->hasMorePages()): ?>
                    <a href="<?php echo e($auctions->nextPageUrl()); ?>"
                       class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        Next
                    </a>
                <?php else: ?>
                    <span class="px-3 py-2 text-sm font-medium text-gray-400 bg-white border border-gray-300 rounded-lg cursor-not-allowed">
                        Next
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <?php else: ?>
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No bids found</h3>
        <p class="text-gray-500 mb-6">
            <?php if(request()->search || request()->item_id || request()->status): ?>
                No bids match your current filters. Try adjusting your search criteria.
            <?php else: ?>
                Get started by creating your first auction bid.
            <?php endif; ?>
        </p>
        <div class="flex justify-center space-x-3">
            <?php if(request()->search || request()->item_id || request()->status): ?>
                <a href="<?php echo e(route('auctions.index')); ?>"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    Clear Filters
                </a>
            <?php endif; ?>
            <a href="<?php echo e(route('auctions.create')); ?>"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add First Bid
            </a>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Include Payment Modal -->
<?php echo $__env->make('modals.pay-auction-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<script>
$(document).ready(function() {
    // Initialize any JavaScript functionality here
    console.log('Modernized auctions page loaded');

    // Initialize DataTable using the modernized module
    ModernizedDataTable.init('.js-datatable', 'js-datatable', [0, 'asc']);

    // Enhanced table interactions
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(2px)';
        });
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // Enhanced button hover effects
    const actionButtons = document.querySelectorAll('.inline-flex');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Auto-submit search form with debounce
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }

    // Enhanced filter animations
    const filterSelects = document.querySelectorAll('select');
    filterSelects.forEach(select => {
        select.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        select.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});

// Enhanced action functions with loading states and error handling

function acceptBid(auctionId, bidderName, itemName) {
    // Show confirmation dialog with details
    const confirmed = confirm(`Are you sure you want to accept this bid?\n\nBidder: ${bidderName}\nItem: ${itemName}\n\nThis action cannot be undone.`);

    if (!confirmed) return;

    // Find the button and show loading state
    const button = event.target.closest('.accept-bid-btn');
    const btnText = button.querySelector('.btn-text');
    const loadingSpinner = button.querySelector('.loading-spinner');

    // Set loading state
    button.disabled = true;
    btnText.textContent = 'Processing...';
    loadingSpinner.classList.remove('hidden');
    button.classList.add('opacity-75');

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = `/accept-bid/${auctionId}`;

    // Add CSRF token for security
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }

    document.body.appendChild(form);
    form.submit();
}

function openPaymentModal(auctionId) {
    if (typeof payAuctionApp !== 'undefined') {
        try {
            // Show loading state
            showNotification('Loading auction details...', 'info');

            payAuctionApp.getAuction(auctionId);

            // Use Bootstrap 5 modal
            const modal = new bootstrap.Modal(document.querySelector('.pay-bid-modal'));
            modal.show();

            // Hide loading notification after modal opens
            setTimeout(() => hideNotification(), 1000);
        } catch (error) {
            console.error('Error opening payment modal:', error);
            showNotification('Failed to open payment modal. Please try again.', 'error');
        }
    } else {
        showNotification('Payment system not available. Please refresh the page.', 'error');
    }
}

// Enhanced notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    hideNotification();

    const notification = document.createElement('div');
    notification.id = 'auction-notification';
    notification.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    // Set colors based on type
    switch (type) {
        case 'success':
            notification.className += ' bg-green-500 text-white';
            break;
        case 'error':
            notification.className += ' bg-red-500 text-white';
            break;
        case 'warning':
            notification.className += ' bg-yellow-500 text-white';
            break;
        default:
            notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <span class="mr-3">${message}</span>
            <button onclick="hideNotification()" class="ml-2 text-white hover:text-gray-200">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto-hide after 5 seconds
    setTimeout(() => hideNotification(), 5000);
}

function hideNotification() {
    const notification = document.getElementById('auction-notification');
    if (notification) {
        notification.classList.add('translate-x-full');
        setTimeout(() => notification.remove(), 300);
    }
}

// Enhanced form submission with loading states
function enhanceFormSubmissions() {
    // Enhance payment form
    const paymentForm = document.querySelector('form[action="/sale-bid"]');
    if (paymentForm) {
        paymentForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = `
                    <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing Payment...
                `;
            }
        });
    }
}

// Enhanced table interactions
function enhanceTableInteractions() {
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        // Add hover effects
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 104, 255, 0.1)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
            this.style.boxShadow = '';
        });

        // Add click to view functionality
        const viewBtn = row.querySelector('a[href*="/auctions/"]');
        if (viewBtn) {
            row.style.cursor = 'pointer';
            row.addEventListener('click', function(e) {
                // Don't trigger if clicking on buttons or links
                if (e.target.closest('button, a, form')) return;

                viewBtn.click();
            });
        }
    });
});

// Initialize enhanced functionality
document.addEventListener('DOMContentLoaded', function() {
    enhanceFormSubmissions();
    enhanceTableInteractions();

    // Show success/error messages from Laravel session
    <?php if(session('success')): ?>
        showNotification('<?php echo e(session('success')); ?>', 'success');
    <?php endif; ?>

    <?php if(session('error')): ?>
        showNotification('<?php echo e(session('error')); ?>', 'error');
    <?php endif; ?>

    <?php if($errors->any()): ?>
        showNotification('<?php echo e($errors->first()); ?>', 'error');
    <?php endif; ?>
});
</script>

<?php $__env->startPush('styles'); ?>
<style>
/* Additional custom styles for enhanced UX */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    box-shadow: 0 4px 12px rgba(0, 104, 255, 0.1);
}

.inline-flex {
    transition: all 0.2s ease;
}

.filter-container select:focus,
.filter-container input:focus {
    box-shadow: 0 0 0 3px rgba(0, 104, 255, 0.1);
}

/* Enhanced loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Better mobile responsiveness */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: 0.75rem;
        overflow: hidden;
    }

    .table th,
    .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }

    .inline-flex {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
}

/* Enhanced status indicators */
.status-indicator {
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.modernized-admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/admin/modernized-auctions.blade.php ENDPATH**/ ?>