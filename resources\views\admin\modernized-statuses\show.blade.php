@extends('layouts.modernized-admin')

@section('title', 'View Status - Vertigo AMS')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex-1 min-w-0">
                    <nav class="flex mb-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li class="inline-flex items-center">
                                <a href="{{ route('admin.statuses.index') }}" class="text-gray-500 hover:text-gray-700 text-sm font-medium">
                                    Status
                                </a>
                            </li>
                            <li>
                                <div class="flex items-center">
                                    <svg class="flex-shrink-0 h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="ml-1 text-sm font-medium text-gray-500">View Status</span>
                                </div>
                            </li>
                        </ol>
                    </nav>
                    <h1 class="text-2xl font-bold text-gray-900 sm:text-3xl">{{ $status->name }}</h1>
                    <p class="mt-1 text-sm text-gray-500">Status details and information</p>
                </div>
                <div class="mt-4 sm:mt-0 sm:ml-4 flex space-x-3">
                    @can('update', $status)
                    <a href="{{ route('admin.statuses.edit', $status) }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white gradient-primary hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Status
                    </a>
                    @endcan
                    
                    @can('view-any', App\Models\Status::class)
                    <a href="{{ route('admin.statuses.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Status
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        <div class="max-w-4xl mx-auto space-y-8">
            
            <!-- Status Information Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-medium text-gray-900">Status Information</h3>
                    <p class="mt-1 text-sm text-gray-500">Basic status details and metadata</p>
                </div>
                <div class="px-6 py-6">
                    <div class="flex items-center space-x-6 mb-6">
                        <div class="flex-shrink-0">
                            <div class="h-20 w-20 rounded-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center">
                                <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h2 class="text-xl font-bold text-gray-900">{{ $status->name }}</h2>
                            <p class="text-gray-600">Status ID: {{ $status->id }}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Status Name</dt>
                            <dd class="text-sm text-gray-900">{{ $status->name ?? '-' }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Status ID</dt>
                            <dd class="text-sm text-gray-900">{{ $status->id }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Created</dt>
                            <dd class="text-sm text-gray-900">{{ $status->created_at ? $status->created_at->format('M d, Y H:i') : '-' }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Last Updated</dt>
                            <dd class="text-sm text-gray-900">{{ $status->updated_at ? $status->updated_at->format('M d, Y H:i') : '-' }}</dd>
                        </div>

                        @if($status->description)
                        <div class="md:col-span-2">
                            <dt class="text-sm font-medium text-gray-500 mb-1">Description</dt>
                            <dd class="text-sm text-gray-900">{{ $status->description }}</dd>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Usage Information Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-medium text-gray-900">Usage Information</h3>
                    <p class="mt-1 text-sm text-gray-500">Where this status is being used in the system</p>
                </div>
                <div class="px-6 py-6">
                    @php
                        $usersWithStatus = \App\Models\User::where('status_id', $status->id)->count();
                        $branchesWithStatus = \App\Models\Branch::where('status_id', $status->id)->count();
                        $advertsWithStatus = \App\Models\Advert::where('status_id', $status->id)->count();
                    @endphp
                    
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">{{ $usersWithStatus }}</div>
                            <div class="text-sm text-blue-800">Users</div>
                        </div>
                        
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">{{ $branchesWithStatus }}</div>
                            <div class="text-sm text-green-800">Branches</div>
                        </div>
                        
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600">{{ $advertsWithStatus }}</div>
                            <div class="text-sm text-purple-800">Adverts</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Records Card -->
            @if($usersWithStatus > 0)
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-medium text-gray-900">Users with this Status</h3>
                    <p class="mt-1 text-sm text-gray-500">Users currently assigned this status</p>
                </div>
                <div class="px-6 py-6">
                    @php
                        $users = \App\Models\User::where('status_id', $status->id)->take(10)->get();
                    @endphp
                    
                    @if($users->count() > 0)
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($users as $user)
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <img class="h-8 w-8 rounded-full object-cover border-2 border-gray-200" 
                                             src="{{ $user->image ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=0068ff&background=f0f9ff&size=32' }}" 
                                             alt="{{ $user->name }}">
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="text-sm font-medium text-gray-900">{{ $user->name }}</h4>
                                        <p class="text-xs text-gray-500">{{ $user->email }}</p>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        
                        @if($usersWithStatus > 10)
                        <div class="mt-4 text-center">
                            <p class="text-sm text-gray-500">And {{ $usersWithStatus - 10 }} more users...</p>
                        </div>
                        @endif
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
