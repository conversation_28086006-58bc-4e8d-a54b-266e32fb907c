<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use App\Models\Auction;
use App\Models\Sale;
use App\Models\Item;
use Illuminate\Http\Request;
use App\Http\Requests\OrderStoreRequest;
use App\Http\Requests\OrderUpdateRequest;

use Facades\App\Libraries\ProductHandler;
use Facades\App\Libraries\InvoiceHandler;
use Facades\App\Cache\Repo;

class OrderController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Order::class);

        $orders = Repo::getOrderCursor()->get();

        return view('app.orders.index', compact('orders'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Order::class);

        $users = User::pluck('name', 'id');
        $items = Repo::getItemCursor()->whereNull('closed_by')->get();
        return view('app.orders.create', compact('users', 'items'));
    }

    /**
     * @param \App\Http\Requests\OrderStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(OrderStoreRequest $request)
    {
        $this->authorize('create', Order::class);

        $validated = $request->validated();

        $data = _array( $request->order );
        $sales  = _array( _from($data, "sales") );
        $order = InvoiceHandler::createOrder($data, $sales);

        return redirect()
            ->route('orders.show', $order)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Order $order)
    {
        $this->authorize('view', $order);
        return view('app.orders.show', compact('order'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Order $order)
    {
        $this->authorize('update', $order);

        $users = User::pluck('name', 'id');
        $items = Item::get();
        $order->sales = $order->sales()->with('item')->get();
        return view('app.orders.edit', compact('order', 'items'));
    }

    /**
     * @param \App\Http\Requests\OrderUpdateRequest $request
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function update(OrderUpdateRequest $request, Order $order)
    {
        $this->authorize('update', $order);

        $validated = $request->validated();

        $data  = _array( $request->order );
        $sales = _array( _from($data, "sales") );

        unset($data['created_by']);
        unset($data['payments']); 

        $order = InvoiceHandler::updateOrder($data, $sales);

        return redirect()
            ->route('orders.show', $order)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Order $order)
    {
        $this->authorize('delete', $order);

        $order->delete();

        return redirect()
            ->route('orders.index')
            ->withSuccess(__('crud.common.removed'));
    }



    public function ajaxOrder(Request $request, Order $order){
        $order->payments;
        $order->sales = $order->sales()->with('item')->get();
        return response( $order );
    }

    public function importOrder(Request $request){

        \Excel::import(new \App\Imports\ImportOrder, $request->products);

        $orders = Order::where("sub_total", 0)->get();

        foreach($orders as $order){

            if( $order ) {

                $sub_total = $order->sales()->get()->sum("buying_amount");
                $discount = $order->sales()->get()->sum("discount");
                $amount_total = $sub_total - $discount;
                $data = [];
                $data["sub_total"] = $sub_total;    
                $data["amount_total"] = $amount_total;    
                $data["amount_paid"] = $amount_total;    
                $data["discount"] = $discount;   

                $order->update($data);
                $order->payments()->delete();
                InvoiceHandler::addPayment($order, [
                    "amount" => $order->amount_paid,
                    "balance" => $order->amount_total - $order->amount_paid
                ]);            
            }

        }

        return redirect()
            ->back()
            ->withSuccess("Products Imported Successfully");    
    }


    public function saleBid(Request $request) {
        $request->validate([
            'auction_id' => 'required|exists:auctions,id',
            'comment' => 'required|string|max:500'
        ]);

        try {
            $auction = Auction::find(request()->auction_id);

            if (!$auction) {
                return redirect()->back()->withError("Auction not found.");
            }

            if (!$auction->closed_by || $auction->tagged_by) {
                return redirect()->back()->withError("This auction is not eligible for payment.");
            }

            $order = InvoiceHandler::createOrderFromAuction($auction);

            // Check if request came from modernized interface
            $referer = $request->header('referer');
            if ($referer && str_contains($referer, 'auctions')) {
                return redirect()->route('auctions.index')->withSuccess("Payment added successfully for auction #{$auction->id}");
            }

            return redirect()->back()->withSuccess("Payment added successfully");
        } catch (\Exception $e) {
            \Log::error('Payment processing failed', [
                'auction_id' => request()->auction_id,
                'error' => $e->getMessage()
            ]);
            return redirect()->back()->withError("Failed to process payment. Please try again.");
        }
    }






    // Modernized Admin Interface Methods

    /**
     * Display modernized sales index page
     */
    public function modernizedIndex(Request $request)
    {
        $this->authorize('view-any', Order::class);

        $orders = Repo::getOrderCursor()
            ->when($request->search, function($query, $search) {
                return $query->where(function($q) use ($search) {
                    $q->where('order_id', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhereHas('user', function($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%");
                      });
                });
            })
            ->when($request->auction_type_id, function($query, $auctionTypeId) {
                return $query->whereHas('auction', function($auctionQuery) use ($auctionTypeId) {
                    $auctionQuery->where('auction_type_id', $auctionTypeId);
                });
            })
            ->when($request->user_id, function($query, $userId) {
                return $query->where('user_id', $userId);
            })
            ->when($request->from && $request->to, function($query) use ($request) {
                return $query->whereBetween('created_at', [$request->from, $request->to]);
            })
            ->paginate(10);

        return view('app.orders.modernized-index', compact('orders'));
    }

    /**
     * Show modernized create form
     */
    public function modernizedCreate(Request $request)
    {
        $this->authorize('create', Order::class);

        $users = User::pluck('name', 'id');
        $items = Repo::getItemCursor()->whereNull('closed_by')->get();

        return view('app.orders.modernized-create', compact('users', 'items'));
    }

    /**
     * Display modernized sale details
     */
    public function modernizedShow(Request $request, Order $order)
    {
        $this->authorize('view', $order);

        return view('app.orders.modernized-show', compact('order'));
    }

    /**
     * Show modernized edit form
     */
    public function modernizedEdit(Request $request, Order $order)
    {
        $this->authorize('update', $order);

        $users = User::pluck('name', 'id');
        $items = Repo::getItemCursor()->whereNull('closed_by')->get();

        return view('app.orders.modernized-edit', compact('order', 'users', 'items'));
    }

    /**
     * Bulk delete orders
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id'
        ]);

        $orders = Order::whereIn('id', $request->order_ids)->get();

        foreach ($orders as $order) {
            $this->authorize('delete', $order);

            // Only delete if not approved
            if (!$order->approved_by) {
                $order->delete();
            }
        }

        return redirect()->route('orders.index')
            ->with('success', 'Selected orders have been deleted successfully.');
    }

    /**
     * Bulk export orders
     */
    public function bulkExport(Request $request)
    {
        $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id'
        ]);

        $orders = Order::with(['user', 'sales.item'])
            ->whereIn('id', $request->order_ids)
            ->get();

        $filename = 'orders_export_' . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($orders) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Order ID',
                'Customer',
                'Total Amount',
                'Discount',
                'Amount Paid',
                'Balance',
                'Status',
                'Created Date',
                'Items Count'
            ]);

            // CSV data
            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->order_id,
                    $order->user->name ?? 'N/A',
                    $order->amount_total,
                    $order->discount,
                    $order->amount_paid,
                    $order->amount_total - $order->amount_paid,
                    $order->approved_by ? 'Approved' : 'Pending',
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->sales->count()
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
