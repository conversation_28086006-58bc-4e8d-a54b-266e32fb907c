<?php if (isset($component)) { $__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0 = $component; } ?>
<?php $component = App\View\Components\MdModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('md-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\MdModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
	 <?php $__env->slot('class', null, []); ?> frontend-modal-register <?php $__env->endSlot(); ?>
	 <?php $__env->slot('title', null, []); ?>  <?php $__env->endSlot(); ?>

  <div class="lezada-form p-4 bg-white">
    <form action="/register" method="POST">
      <?php echo csrf_field(); ?>
      <div class="row">
        <div class="col-lg-12">
          <!--=======  login title  =======-->

          <div class="section-title section-title--login text-center mb-50">
            <img src="<?php echo e(asset('assets/svg/logo/Trust - Badge dark Outline on Light BG.svg')); ?>" alt="" height="100">
            <p class="mt-2">Create your account</p>
            <?php if(session('error') ): ?>
              <p class="text-danger"><?php echo e(session('error')); ?></p>
             <?php endif; ?>

            
            <?php if($errors->any()): ?>
                 <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <p class="text-danger"><?php echo e($error); ?></p>
                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
             <?php endif; ?>
          </div>

          <!--=======  End of login title  =======-->
        </div>

        <div class="col-lg-12 mb-20">
          Full Name
          <input type="text" name="name" placeholder="Enter Full Name" required>
        </div>

        <div class="col-lg-12 mb-20">
          Phone
          <input type="text" name="phone" placeholder="Enter Phone" required>
        </div>      

        <div class="col-lg-12 mb-20">
          Email Address
          <input type="text" name="email" placeholder="Enter Email" required>
        </div>                

        <div class="col-lg-12 mb-20">
          Password
          <input type="password" name="password" placeholder="Enter Password" required>
        </div>
        <div class="col-lg-12 mb-20">
          Password Confirmation
          <input type="password" name="password_confirmation" placeholder="Re-type Password" required>
        </div>

        <div class="col-lg-12 text-center mb-20">
          <button class="lezada-button lezada-button--medium">Signup</button>
        </div>
      </div>
    </form>
  </div>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0)): ?>
<?php $component = $__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0; ?>
<?php unset($__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0); ?>
<?php endif; ?>





<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/frontend-register-modal.blade.php ENDPATH**/ ?>