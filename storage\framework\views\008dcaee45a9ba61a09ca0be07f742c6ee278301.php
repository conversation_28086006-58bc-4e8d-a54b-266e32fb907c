

<?php $__env->startSection('content'); ?>

    <!-- <div class="position-fixed top-0 end-0 start-0 bg-img-start" style="height: 32rem; background-image: url(assets/svg/components/card-61.svg);"> -->
    <div class="position-fixed top-0 end-0 start-0 bg-img-start" style="height: 32rem; background:#f2f2f2;">
      <!-- Shape -->
      <div class="shape shape-bottom zi-1">
        <svg preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 1921 273">
          <polygon fill="#fff" points="0,273 1921,273 1921,0 " />
        </svg>
      </div>
      <!-- End Shape -->
    </div>

    <!-- Content -->
    <div class="container py-5 py-sm-7 position-relative zi-2">
      <a class="d-flex justify-content-center mb-5" href="/">
        <img class="zi-2" src="<?php echo e(asset('assets/svg/logo/Trust - Badge dark Outline on Light BG.svg')); ?>" alt="Image Description" height="130">
      </a>

      <div class="mx-auto" style="max-width: 26rem;">
        <!-- Card -->
        <div class="card card-lg mb-5">
          <div class="card-body">
            <!-- Form -->
            <form action="/login" method="POST" class="js-validate needs-validation" novalidate>
              <div class="text-center">
                <?php echo csrf_field(); ?>

                <div class="mb-5">
                  <h1 class="display-5">Log in to your account</h1>
                </div>

                <?php if(session('error') ): ?>
                  <span class="divider-center text-danger mb-4"><?php echo e(session('error')); ?></span>               
                <?php endif; ?>
                    
                <?php if($errors->any()): ?>
                 <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="divider-center text-danger mb-4"><?php echo e($error); ?></span>     
                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>

              </div>

              <!-- Form -->
              <div class="mb-4">
                <label class="form-label" for="signinSrEmail">Your email</label>
                <input type="text" class="form-control" name="username" id="signinSrEmail" tabindex="1" placeholder="" aria-label="Username" required>
                <span class="invalid-feedback">Please enter a valid email address.</span>
              </div>
              <!-- End Form -->

              <!-- Form -->
              <div class="mb-4">
                <label class="form-label w-100" for="signupSrPassword" tabindex="0">
                  <span class="d-flex justify-content-between align-items-center">
                    <span>Password</span>
                    <a class="form-label-link mb-0" href="authentication-reset-password-basic.html">Forgot Password?</a>
                  </span>
                </label>

                <div class="input-group input-group-merge" data-hs-validation-validate-class>
                  <input type="password" class="js-toggle-password form-control" name="password" id="signupSrPassword" placeholder="" aria-label="" required minlength="8" data-hs-toggle-password-options='{
                           "target": "#changePassTarget",
                           "defaultClass": "bi-eye-slash",
                           "showClass": "bi-eye",
                           "classChangeTarget": "#changePassIcon"
                         }'>
                  <a id="changePassTarget" class="input-group-append input-group-text" href="javascript:;">
                    <i id="changePassIcon" class="bi-eye"></i>
                  </a>
                </div>

                <span class="invalid-feedback">Please enter a valid password.</span>
              </div>
              <!-- End Form -->

              <!-- Form Check -->
              <div class="form-check mb-4">
                <input class="form-check-input" type="checkbox" value="" id="termsCheckbox">
                <label class="form-check-label" for="termsCheckbox">
                  Remember me
                </label>
              </div>
              <!-- End Form Check -->

              <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-sm">Log in</button>
              </div>
            </form>
            <!-- End Form -->
          </div>
        </div>
        <!-- End Card -->
      </div>
    </div>
    <!-- End Content -->

<style>
body {
  overflow: hidden;
}
.btn-primary {
  background-color: #003472;
  border-color: #003472;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
  background-color: #00275a;
  border-color: #00275a;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/auth/admin-login.blade.php ENDPATH**/ ?>