<!DOCTYPE html>
<html class="no-js" lang="zxx">


<!-- Mirrored from htmldemo.hasthemes.com/lezada-preview/lezada/shop-left-sidebar.html by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 26 May 2021 07:34:49 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo e(env('APP_NAME')); ?></title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Favicon -->
    <!-- <link rel="icon" href="<?php echo e(asset('shopping/images/favicon.ico')); ?>"> -->
        <!-- Favicon -->
    <link rel="shortcut icon" href="favicon.png">

    <!-- Fonts -->
    <!-- <link rel="dns-prefetch" href="//fonts.gstatic.com"> -->
    <!-- <link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"> -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <!-- CSS
    ============================================ -->
    <!-- Bootstrap CSS -->
    <link href="<?php echo e(asset('shopping/css/bootstrap.min.css')); ?>" rel="stylesheet">

    <!-- FontAwesome CSS -->
    <link href="<?php echo e(asset('shopping/css/font-awesome.min.css')); ?>" rel="stylesheet">

    <!-- Ionicons CSS -->
    <link href="<?php echo e(asset('shopping/css/ionicons.min.css')); ?>" rel="stylesheet">

    <!-- Themify CSS -->
    <link href="<?php echo e(asset('shopping/css/themify-icons.css')); ?>" rel="stylesheet">

    <!-- Plugins CSS -->
    <link href="<?php echo e(asset('shopping/css/plugins.css')); ?>" rel="stylesheet">

    <!-- Helper CSS -->
    <link href="<?php echo e(asset('shopping/css/helper.css')); ?>" rel="stylesheet">

    <!-- Main CSS -->
    <link href="<?php echo e(asset('shopping/css/main.css')); ?>" rel="stylesheet">

    <!-- Modernizer JS -->
    <script src="<?php echo e(asset('shopping/js/vendor/modernizr-2.8.3.min.js')); ?>"></script>
    
    <!-- Icons -->
    <link href="https://unpkg.com/ionicons@4.5.10-0/dist/css/ionicons.min.css" rel="stylesheet">

    <!-- Revolution Slider CSS -->
    <link href="<?php echo e(asset('/shopping/revolution/css/settings.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('/shopping/revolution/css/navigation.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('/shopping/revolution/custom-setting.css')); ?>" rel="stylesheet">


    <!-- Add the slick-theme.css if you want default styling -->
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>

    <script type="text/javascript" src="<?php echo e(asset('js/vue.min.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('js/axios.min.js')); ?>"></script>

<!-- Bootstrap icons -->
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('css/custom.css')); ?>">
    <style type="text/css">
      .cursor{
          cursor: pointer;
      }

      .loader-container {
          width: 100%;
          height: 100vh;
          position: fixed;
          background: #000
              url("https://media.giphy.com/media/8agqybiK5LW8qrG3vJ/giphy.gif") center
              no-repeat;
          z-index: 1;
      }

      .loader-container {
          width: 100%;
          height: 100vh;
          display: flex;
          justify-content: center;
          align-items: center;
          position: fixed;
          background: #E4E5E8;
          z-index: 99;
      }
      
      .spinner {
          width: 64px;
          height: 64px;
          border: 8px solid;
          border-color: #071c49 transparent #071c49 transparent;
          border-radius: 50%;
          animation: spin-anim 1.2s linear infinite;
      }

      @keyframes spin-anim {
          0% {
              transform: rotate(0deg);
          }
          100% {
              transform: rotate(360deg);
          }
      }

      .modal {
        overflow-y:auto;
      }

      i.bi{
        font-size: 24px;
      }
    </style>

</head>

<body>




    <?php echo $__env->make('frontend.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <div class="loader-container">
      <div class="spinner"></div>
    </div>
    
    <?php echo $__env->make('frontend.breadcrump', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php if(session('success') ): ?>
    <p class="m-2 alert-success text-center alert "><?php echo e(session('success')); ?></p>
    <?php endif; ?>

    <?php if(session('error') ): ?>
      <p class="m-2 alert-danger text-center alert "><?php echo e(session('error')); ?></p>
    <?php endif; ?>

    <?php echo $__env->make('modals.preview-item-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->yieldContent('content'); ?>
    
    <?php echo $__env->make('modals.frontend-login-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->make('modals.frontend-register-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->make('modals.bid-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->make('frontend.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->make('modals.chat-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->yieldPushContent('modals'); ?>
    
    <?php echo \Livewire\Livewire::scripts(); ?>

  
    <!--=====  End of quick view  ======-->

    <!-- scroll to top  -->
    <a href="#" class="scroll-top"></a>
    <!-- end of scroll to top -->

    <!-- JS
    ============================================ -->
    <!-- jQuery JS -->
    <script src="<?php echo e(asset('shopping/js/vendor/jquery.min.js')); ?>"></script>

    <!-- Popper JS -->
    <script src="<?php echo e(asset('shopping/js/popper.min.js')); ?>"></script>

    <!-- Bootstrap JS -->
    <script src="<?php echo e(asset('shopping/js/bootstrap.min.js')); ?>"></script>

    <!-- Plugins JS -->
    <script src="<?php echo e(asset('shopping/js/plugins.js')); ?>"></script>

    <!-- Main JS -->
    <script src="<?php echo e(asset('shopping/js/main.js')); ?>"></script>


    <!-- Revolution Slider JS -->
    <script src="<?php echo e(asset('/shopping/revolution/js/jquery.themepunch.revolution.min.js')); ?>"></script>
    <script src="<?php echo e(asset('/shopping/revolution/js/jquery.themepunch.tools.min.js')); ?>"></script>
    <script src="<?php echo e(asset('/shopping/revolution/revolution-active.js')); ?>"></script>

    <!-- SLIDER REVOLUTION 5.0 EXTENSIONS  (Load Extensions only on Local File Systems !  The following part can be removed on Server for On Demand Loading) -->
    <script type="text/javascript" src="<?php echo e(asset('/shopping/revolution/js/extensions/revolution.extension.kenburn.min.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('/shopping/revolution/js/extensions/revolution.extension.slideanims.min.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('/shopping/revolution/js/extensions/revolution.extension.actions.min.js')); ?>"></script>
    <script type="text/javascript"
        src="<?php echo e(asset('/shopping/revolution/js/extensions/revolution.extension.layeranimation.min.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('/shopping/revolution/js/extensions/revolution.extension.navigation.min.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('/shopping/revolution/js/extensions/revolution.extension.parallax.min.js')); ?>"></script>

    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

     <?php echo $__env->yieldPushContent('scripts'); ?>

    <script type="text/javascript">
        $("document").ready( function(){
          $(".loader-container").fadeOut();

            $('.breadcrump').removeClass('d-none')
            $('.breadcrump').slick({
                // dots: true,
                infinite: true,
                centerMode: true,
                autoplay: true,
                fade: true,
                arrows:false,
                cssEase: 'linear',
                speed: 500
            });
        });
    </script>


    <?php if(auth()->guard()->check()): ?>
    <?php else: ?>
    <script type="text/javascript">

      // $('document').ready(function(){

      //   $(".frontend-modal-login").modal('show');

      // });

    </script>

    <?php endif; ?>

</body>

</html><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/layouts/frontend-layout.blade.php ENDPATH**/ ?>