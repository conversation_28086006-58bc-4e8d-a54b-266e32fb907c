  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand-lg navbar-bordered navbar-spacer-y-0 flex-lg-column">
    <div class="navbar-dark w-100 py-2 admin-header-top">
      <div class="container">
        <div class="navbar-nav-wrap">
          <!-- Logo -->
          <a class="navbar-brand d-flex align-items-center" href="/shop" aria-label="<?php echo e(env('APP_NAME')); ?>">
            <img src="<?php echo e(asset('assets/img/logo/logo_white_512x512.png')); ?>" alt="Logo" height="40" class="me-2">
            <span class="text-white"><?php echo e(env('APP_NAME')); ?></span>
          </a>
          <!-- End Logo -->

          <!-- Content Start -->
          <div class="navbar-nav-wrap-content-start">
            <!-- Search Form -->
            <div class="d-none d-lg-block">
              <div class="dropdown ms-2">
                <!-- Input Group -->
                <div class="d-none d-lg-block">
                  <div class="input-group input-group-merge input-group-borderless input-group-hover-light navbar-input-group">
                    <div class="input-group-prepend input-group-text">
                      <i class="bi-search"></i>
                    </div>

                    <input type="search" class="js-form-search form-control" placeholder="<?php echo e(auth()->user()->branch->name ?? 'Please select branch'); ?>" aria-label="<?php echo e(auth()->user()->branch->name ?? 'Please select branch'); ?>" data-hs-form-search-options='{
                           "clearIcon": "#clearSearchResultsIcon",
                           "dropMenuElement": "#searchDropdownMenu",
                           "dropMenuOffset": 20,
                           "toggleIconOnFocus": true,
                           "activeClass": "focus"
                         }'>
                    <a class="input-group-append input-group-text" href="javascript:;">
                      <i id="clearSearchResultsIcon" class="bi-x-lg" style="display: none;"></i>
                    </a>
                  </div>
                </div>

                <button class="js-form-search js-form-search-mobile-toggle btn btn-ghost-secondary btn-icon rounded-circle d-lg-none" type="button" data-hs-form-search-options='{
                           "clearIcon": "#clearSearchResultsIcon",
                           "dropMenuElement": "#searchDropdownMenu",
                           "dropMenuOffset": 20,
                           "toggleIconOnFocus": true,
                           "activeClass": "focus"
                         }'>
                  <i class="bi-search"></i>
                </button>
                <!-- End Input Group -->

                <!-- Card Search Content -->
                <div id="searchDropdownMenu" class="hs-form-search-menu-content dropdown-menu dropdown-menu-form-search navbar-dropdown-menu-borderless">
                  <div class="card">
                    <!-- Body -->
                    <div class="card-body-height">
                      <div class="d-lg-none">
                        <div class="input-group input-group-merge navbar-input-group mb-5">
                          <div class="input-group-prepend input-group-text">
                            <i class="bi-search"></i>
                          </div>

                          <input type="search" class="form-control" placeholder="<?php echo e(auth()->user()->branch->name ?? 'Please select branch'); ?>" aria-label="<?php echo e(auth()->user()->branch->name ?? 'Please select branch'); ?>">
                          <a class="input-group-append input-group-text" href="javascript:;">
                            <i class="bi-x-lg"></i>
                          </a>
                        </div>
                      </div>

                      <span class="dropdown-header">Select branch</span>

                      <div class="dropdown-item bg-transparent text-wrap">
                    <!--     <a class="btn btn-soft-dark btn-xs rounded-pill" href="/home">
                          Gulp <i class="bi-search ms-1"></i>
                        </a> -->
                        <?php $__currentLoopData = \App\Models\Branch::pluck("name", "id"); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a class="btn btn-soft-<?php echo e($key == auth()->user()->branch_id? 'primary' : 'dark'); ?> btn-xs rounded-pill" href="/set-branch/<?php echo e($key); ?>">
                          <?php echo e($value); ?> <i class="bi-search ms-1"></i>
                        </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                      </div>

                    </div>
                    <!-- End Body -->

                    <!-- Footer -->
                    <a class="card-footer text-center" href="/home">
                      See all results <i class="bi-chevron-right small"></i>
                    </a>
                    <!-- End Footer -->
                  </div>
                </div>
                <!-- End Card Search Content -->

              </div>

            </div>
            <!-- End Search Form -->
          </div>
          <!-- End Content Start -->

          <!-- Content End -->
          <div class="navbar-nav-wrap-content-end">
            <!-- Navbar -->
            <ul class="navbar-nav">
              <li class="nav-item d-none d-md-inline-block">
                <!-- Notification -->
                <div class="dropdown">
                  <?php $expiryItems = \Facades\App\Cache\Repo::expiringItems() ?>

                  <button type="button" class="btn btn-ghost-light btn-icon rounded-circle" id="navbarNotificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside" data-bs-dropdown-animation>
                    <i class="bi-bell"></i>
                    <span class="badge rounded-circle m-1 bg-soft-light text-warning">
                      <?php echo e(_number($expiryItems->count() )); ?>

                    </span>
                    <!-- <span class="btn-status btn-sm-status btn-status-danger"></span> -->
                  </button>

                  <div class="dropdown-menu dropdown-menu-end dropdown-card navbar-dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="navbarNotificationsDropdown" style="width: 25rem;">
                    <div class="card">
                      <!-- Header -->
                      <div class="card-header card-header-content-between">
                        <h4 class="card-title mb-0">Notifications</h4>
                      </div>
                      <!-- End Header -->



                    <!-- Nav -->
                      <ul class="nav nav-segment nav-justified m-2" role="tablist">
                        <li class="nav-item bg-white">
                          <a class="nav-link active" id="nav-one-eg1-tab" href="#nav-one-eg1" data-bs-toggle="pill" data-bs-target="#nav-one-eg1" role="tab" aria-controls="nav-one-eg1" aria-selected="true">Messages</a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link" id="nav-two-eg1-tab" href="#nav-two-eg1" data-bs-toggle="pill" data-bs-target="#nav-two-eg1" role="tab" aria-controls="nav-two-eg1" aria-selected="false">Expiring Items (<?php echo e(_number($expiryItems->count())); ?>)</a>
                        </li>

                      </ul>

                      <!-- Tab Content -->
                      <div class="tab-content">
                        <div class="tab-pane fade show active" id="nav-one-eg1" role="tabpanel" aria-labelledby="nav-one-eg1-tab">
                          <ul class="list-group list-group-flush navbar-card-list-group">
           <!--                  <li class="list-group-item form-check-select">
                              <div class="row">
                                <div class="col-auto">
                                  <div class="d-flex align-items-center">
                                    <div class="form-check">
                                      <input class="form-check-input" type="checkbox" value="" id="notificationCheck1" checked>
                                      <label class="form-check-label" for="notificationCheck1"></label>
                                      <span class="form-check-stretched-bg"></span>
                                    </div>
                                    <img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img3.jpg" alt="Image Description">
                                  </div>
                                </div>

                                <div class="col ms-n2">
                                  <h5 class="mb-1">Brian Warner</h5>
                                  <p class="text-body fs-5">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></p>
                                </div>

                                <small class="col-auto text-muted text-cap">2hr</small>
                              </div>

                              <a class="stretched-link" href="#"></a>
                            </li> -->

                          </ul>
                        </div>

                        <div class="tab-pane fade" id="nav-two-eg1" role="tabpanel" aria-labelledby="nav-two-eg1-tab">
                          <ul class="list-group list-group-flush navbar-card-list-group">
                            <?php $__currentLoopData = $expiryItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expiryItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="list-group-item form-check-select">
                              <div class="row">
                                <div class="col-auto">
                                  <div class="d-flex align-items-center">
                                    <div class="form-check">
                                      <input class="form-check-input" type="checkbox" value="" id="notificationCheck1" checked>
                                      <label class="form-check-label" for="notificationCheck1"></label>
                                      <span class="form-check-stretched-bg"></span>
                                    </div>
                                    <img class="avatar avatar-sm avatar-circle" src="<?php echo e($expiryItem->image); ?>" alt="Image Description">
                                  </div>
                                </div>

                                <div class="col ms-n2">
                                  <h5 class="mb-1"><?php echo e($expiryItem->name); ?></h5>
                                  <p class="text-body fs-5">
                                    <?php echo e($expiryItem->reference_number); ?>

                                  <a href="/items/<?php echo e($expiryItem->id); ?>/edit" class="badge bg-success">Edit</a></p>
                                </div>

                                <small class="col-auto text-muted text-cap">
                                  <?php echo e($expiryItem->date_to ? $expiryItem->date_to->diffForHumans() :''); ?>

                                </small>
                              </div>

                              <a class="stretched-link" href="/items/<?php echo e($expiryItem->id); ?>/edit"></a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                          </ul>
                        </div>
                      </div>
                      <!-- End Tab Content -->


                      <!-- Card Footer -->
                      <a class="card-footer text-center" href="/notifications">
                        View all notifications <i class="bi-chevron-right"></i>
                      </a>
                      <!-- End Card Footer -->
                    </div>
                  </div>
                </div>
                <!-- End Notification -->
              </li>

              <li class="nav-item">
                <div class="dropdown ">
                  <!-- <button type="button" class="btn btn-ghost-light btn-icon rounded-circle" id="selectThemeDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-dropdown-animation>

                  </button> -->

                  <div class="dropdown-menu dropdown-menu-end navbar-dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="selectThemeDropdown">
                    <a class="dropdown-item" href="#" data-icon="bi-moon-stars" data-value="auto">
                      <i class="bi-moon-stars me-2"></i>
                      <span class="text-truncate" title="Auto (system default)">Auto (system default)</span>
                    </a>
                    <a class="dropdown-item" href="#" data-icon="bi-brightness-high" data-value="default">
                      <i class="bi-brightness-high me-2"></i>
                      <span class="text-truncate" title="Default (light mode)">Default (light mode)</span>
                    </a>
                    <a class="dropdown-item active" href="#" data-icon="bi-moon" data-value="dark">
                      <i class="bi-moon me-2"></i>
                      <span class="text-truncate" title="Dark">Dark</span>
                    </a>
                  </div>
                </div>

                <!-- End Style Switcher -->
              </li>

              <li class="nav-item">
                <!-- Account -->
                <div class="dropdown">
                  <a class="navbar-dropdown-account-wrapper" href="javascript:;" id="accountNavbarDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside" data-bs-dropdown-animation>
                    <div class="avatar avatar-sm avatar-circle">
                      <img class="avatar-img" src="<?php echo e(auth()->user()->image ?? ''); ?>" alt="Image Description">
                      <span class="avatar-status avatar-sm-status avatar-status-success"></span>
                    </div>
                  </a>

                  <div class="dropdown-menu dropdown-menu-end navbar-dropdown-menu navbar-dropdown-menu-borderless navbar-dropdown-account" aria-labelledby="accountNavbarDropdown" style="width: 16rem;">
                    <div class="dropdown-item-text">
                      <div class="d-flex align-items-center">
                        <div class="avatar avatar-sm avatar-circle">
                          <img class="avatar-img" src="<?php echo e(auth()->user()->image ?? ''); ?>" alt="Image Description">
                        </div>
                        <div class="flex-grow-1 ms-3">
                          <h5 class="mb-0"><?php echo e(auth()->user()->name ?? ''); ?></h5>
                          <p class="card-text text-body"> <?php echo e(auth()->user()->email ?? ''); ?> </p>
                        </div>
                      </div>
                    </div>

                    <?php if( auth()->check() && auth()->user()->isSuperAdmin() ): ?>
                    <div class="dropdown-divider"></div>

                    <a class="dropdown-item" href="/backup">System Backup</a>
                    <?php endif; ?>

                    <div class="dropdown-divider"></div>

                    <!-- Modernized Admin Interface Link -->
                    <a class="dropdown-item d-flex align-items-center" href="<?php echo e(route('admin.index')); ?>">
                      <div class="flex-shrink-0">
                        <div class="avatar avatar-xs avatar-soft-primary avatar-circle">
                          <span class="avatar-initials">
                            <i class="bi-palette"></i>
                          </span>
                        </div>
                      </div>
                      <div class="flex-grow-1 ms-2">
                        <span>New Admin Interface</span>
                        <span class="badge bg-primary ms-1">Preview</span>
                      </div>
                    </a>



                    <div class="dropdown-divider"></div>
                    <form method="POST" action="/logout" id="logout">
                      <?php echo csrf_field(); ?>
                    </form>
                    <a class="dropdown-item" href="#" onclick="logout.submit()">Sign out</a>

                  </div>
                </div>
                <!-- End Account -->
              </li>

              <li class="nav-item">
                <!-- Toggler -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarDoubleLineContainerNavDropdown" aria-controls="navbarDoubleLineContainerNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                  <span class="navbar-toggler-default">
                    <i class="bi-list"></i>
                  </span>
                  <span class="navbar-toggler-toggled">
                    <i class="bi-x"></i>
                  </span>
                </button>
                <!-- End Toggler -->
              </li>
            </ul>
            <!-- End Navbar -->
          </div>
          <!-- End Content End -->
        </div>
      </div>
    </div>















    <div class="container">
      <nav class="js-mega-menu flex-grow-1 admin-nav-menu">
        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="navbarDoubleLineContainerNavDropdown">
          <ul class="navbar-nav">


            <li class="nav-item">
              <a class="nav-link  <?php if( request()->is('/') ): ?> active <?php endif; ?>" href="/home">
                <i class="bi-house-door dropdown-item-icon"></i> Dashboards
              </a>
            </li>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Sale::class)): ?>
            <li class="nav-item">
              <a class="nav-link <?php if( request()->is('orders') ): ?> active <?php endif; ?>" href="/orders" data-placement="left">
                <i class="bi-book dropdown-item-icon"></i> Sales
              </a>
            </li>
            <?php endif; ?>

            <!-- Dashboards -->
            <li class="hs-has-sub-menu nav-item">
              <a id="reportsMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle" href="#" role="button">
                <i class="bi-book dropdown-item-icon"></i> Reports
              </a>

              <!-- Mega Menu -->
              <div class="hs-sub-menu dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="reportsMegaMenu" style="min-width: 14rem;">

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                <a class="dropdown-item <?php if( request()->is('winning-report*') ): ?> active <?php endif; ?>" href="/winners-report"> Winning Bid Report </a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                <a class="dropdown-item <?php if( request()->is('sales-report*') ): ?> active <?php endif; ?>" href="/sales-report">Sales Report</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                <a class="dropdown-item <?php if( request()->is('inventory-report*') ): ?> active <?php endif; ?>" href="/inventory-report">Inventory Report</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                <a class="dropdown-item <?php if( request()->is('refund-list-report*') ): ?> active <?php endif; ?>" href="/refund-list-report">Refund List Report</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                <a class="dropdown-item <?php if( request()->is('deposits-report*') ): ?> active <?php endif; ?>" href="/deposits-report">Deposits Report</a>
                <?php endif; ?>

              </div>
              <!-- End Mega Menu -->
            </li>
            <!-- End Dashboards -->

            <!-- Dashboards -->
            <li class="hs-has-sub-menu nav-item">
              <a id="userManagersMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle   <?php if( request()->is('items*', 'auction-types*', 'statuses*') ): ?> active <?php endif; ?>" href="#" role="button">
                <i class="bi-book dropdown-item-icon"></i>Inventory
              </a>

              <!-- Mega Menu -->
              <div class="hs-sub-menu dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="userManagersMegaMenu" style="min-width: 14rem;">

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Auction::class)): ?>
                <a class="dropdown-item <?php if( request()->is('auctions*') ): ?> active <?php endif; ?>" href="/auctions">Bid List</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Item::class)): ?>
                <a class="dropdown-item <?php if( request()->is('items*') ): ?> active <?php endif; ?>" href="/items">Add/view Items</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AuctionType::class)): ?>
                  <a class="dropdown-item <?php if( request()->is('auction-listing*') ): ?> active <?php endif; ?>" href="/auction-listing">Auction Listing</a>
                <?php endif; ?>

<!--                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Order::class)): ?>
                  <a class="dropdown-item <?php if( request()->is('orders*') ): ?> active <?php endif; ?>" href="/orders">Sales</a>
                <?php endif; ?>
 -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AuctionType::class)): ?>
                  <a class="dropdown-item <?php if( request()->is('auction-types*') ): ?> active <?php endif; ?>" href="/auction-types">Auction Categories</a>
                <?php endif; ?>


                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                <a class="dropdown-item <?php if( request()->is('suppliers*') ): ?> active <?php endif; ?>" href="/suppliers">Suppliers</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Account::class)): ?>
                <a class="dropdown-item <?php if( request()->is('accounts*') ): ?> active <?php endif; ?>" href="/accounts">Accounts</a>
                <?php endif; ?>

              </div>
              <!-- End Mega Menu -->
            </li>
            <!-- End Dashboards -->


            <!-- Dashboards -->
            <li class="hs-has-sub-menu nav-item">
              <a id="userManagersMegaMenu" class="hs-mega-menu-invoker nav-link dropdown-toggle   <?php if( request()->is('users*', 'roles*', 'statuses*') ): ?> active <?php endif; ?>" href="#" role="button"><i class="bi-house-door dropdown-item-icon"></i> User Management</a>

              <!-- Mega Menu -->
              <div class="hs-sub-menu dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="userManagersMegaMenu" style="min-width: 14rem;">

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                <a class="dropdown-item <?php if( request()->is('users*') ): ?> active <?php endif; ?>" href="/users">Users</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Role::class)): ?>
                <a class="dropdown-item <?php if( request()->is('roles*') ): ?> active <?php endif; ?>" href="/roles">Roles</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Branch::class)): ?>
                <a class="dropdown-item <?php if( request()->is('branches*') ): ?> active <?php endif; ?>" href="/branches">Branches</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Role::class)): ?>
                <a class="dropdown-item <?php if( request()->is('settings*') ): ?> active <?php endif; ?>" href="/settings">Settings</a>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Role::class)): ?>
                <a class="dropdown-item <?php if( request()->is('backup*') ): ?> active <?php endif; ?>" href="/backup">Backup</a>
                <?php endif; ?>

              </div>
              <!-- End Mega Menu -->
            </li>
            <!-- End Dashboards -->

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Transaction::class)): ?>
            <li class="nav-item">
              <a class="nav-link <?php if( request()->is('transactions') ): ?> active <?php endif; ?>" href="/transactions" data-placement="left">
                <i class="bi-book dropdown-item-icon"></i> Deposits
              </a>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Advert::class)): ?>
            <li class="nav-item">
              <a class="nav-link <?php if( request()->is('adverts') ): ?> active <?php endif; ?>" href="/adverts" data-placement="left">
                <i class="bi-book dropdown-item-icon"></i> Adverts
              </a>
            </li>
            <?php endif; ?>

            <li class="nav-item">
              <a class="nav-link <?php if( request()->is('shop') ): ?> active <?php endif; ?>" href="/shop" data-placement="left">
                <i class="bi-book dropdown-item-icon"></i> Website
              </a>
            </li>

          </ul>

        </div>
        <!-- End Collapse -->
      </nav>
    </div>
  </header>
  <!-- ========== END HEADER ========== --><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/partial/header.blade.php ENDPATH**/ ?>