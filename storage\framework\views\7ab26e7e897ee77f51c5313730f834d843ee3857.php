
<div class="breadcrumb-area cursor breadcrumb-bg-2">
    <div class="breadcrump d-none">    
        <?php $__currentLoopData = \App\Models\Advert::get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $adItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="banner-container">
                <div>
                    <?php if($adItem->date_to): ?>
                    <input type="hidden" value="<?php echo e($adItem->date_to); ?>" class="df"/>
                    <div class ="banner-content row">
                        <div class="col-md-3">
                            
                        </div>

                        <div class="text-white countdown col-md-6">

                        </div>

                        <div class="col-md-3 location">
                            <div> <i class="bi bi-pin-map-fill"></i> <?php echo e($adItem->description); ?></div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <a href="/register-bid" class="img-fluid img-link">
                        <img src="<?php echo e($adItem->image); ?>" class="img-fluid" style="width: 100%;">
                    </a>
                </div>
                
            </div>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

</div>



<?php $__env->startPush('scripts'); ?>

    <script type="text/javascript">


        function updateCountdown() {
            $df = $('.df').val();
            if(!$df) {
                $(".countdown").html("");
            }
            var targetDate = new Date($df); // Set your target date and time here
            var now = new Date();
            var timeRemaining = targetDate - now;

            if (timeRemaining <= 0) {
                $(".countdown").text("Countdown expired!");
                return;
            }

            var days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
            var hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

            var countdownText = `
                <table class="table table-borderless">
                    <tr class="p-4">
                        <th> ${days} </th>
                        <th> ${hours} </th>
                        <th> ${minutes}  </th>
                        <th> ${seconds} </th>
                    </tr>                    
                    <tr>
                        <td> Days </td>
                        <td> Hours </td>
                        <td> Minutes</td>
                        <td> Seconds</td>
                    </tr>
                </table>
            `;
            $(".countdown").html(countdownText);
        }

        $('document').ready(function(){
            updateCountdown(); // Initial call to update countdown
            setInterval(updateCountdown, 1000); // Update countdown every second
        })

    </script>

<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/frontend/breadcrump.blade.php ENDPATH**/ ?>