

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'searchPlaceholder' => 'Search...',
    'filters' => [],
    'showClearButton' => true
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'searchPlaceholder' => 'Search...',
    'filters' => [],
    'showClearButton' => true
]); ?>
<?php foreach (array_filter(([
    'searchPlaceholder' => 'Search...',
    'filters' => [],
    'showClearButton' => true
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
    <div class="flex-1">
        <h3 class="text-lg font-medium text-gray-900"><?php echo e($title ?? 'Data List'); ?></h3>
        <p class="mt-1 text-sm text-gray-500"><?php echo e($description ?? 'Manage and view data records'); ?></p>
    </div>
    
    <!-- Search and Filter Controls -->
    <div class="flex flex-col sm:flex-row gap-3 lg:w-auto">
        <!-- Search Form -->
        <form method="GET" class="flex items-center">
            <!-- Preserve existing filters -->
            <?php $__currentLoopData = request()->except(['search', 'page']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($value): ?>
                    <input type="hidden" name="<?php echo e($key); ?>" value="<?php echo e($value); ?>">
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            
            <div class="relative flex-1 min-w-0">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text" 
                       name="search" 
                       value="<?php echo e(request()->search); ?>"
                       placeholder="<?php echo e($searchPlaceholder); ?>" 
                       class="block w-full sm:w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
            </div>
            <button type="submit" 
                    class="ml-2 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Search
            </button>
        </form>

        <!-- Additional Filters -->
        <?php $__currentLoopData = $filters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $filter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <form method="GET" class="flex items-center">
            <!-- Preserve search term and other filters -->
            <?php if(request()->search): ?>
                <input type="hidden" name="search" value="<?php echo e(request()->search); ?>">
            <?php endif; ?>
            <?php $__currentLoopData = request()->except([$filter['name'], 'page']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($value && $key !== $filter['name']): ?>
                    <input type="hidden" name="<?php echo e($key); ?>" value="<?php echo e($value); ?>">
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            
            <select class="block w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm" 
                    name="<?php echo e($filter['name']); ?>" 
                    onchange="this.form.submit()" 
                    autocomplete="off">
                <option value=""><?php echo e($filter['placeholder'] ?? 'All Options'); ?></option>
                <?php $__currentLoopData = $filter['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option <?php if(request($filter['name']) == $value): ?> selected <?php endif; ?> value="<?php echo e($value); ?>">
                    <?php echo e($label); ?>

                </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </form>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <!-- Clear Filters -->
        <?php if($showClearButton && (request()->search || collect($filters)->some(fn($filter) => request($filter['name'])))): ?>
        <a href="<?php echo e(url()->current()); ?>" 
           class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            Clear
        </a>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/components/modernized/table-search.blade.php ENDPATH**/ ?>