


<?php $__env->startSection('content'); ?>

    <!--=============================================
    =            shop page content         =
    =============================================-->

    <div class="shop-page-wrapper" id="welcome-el">

        <!--=======  shop page header  =======-->

        <div class="shop-page-header">
            <div class="container wide"> 
                <div class="row align-items-center">

                    <div class="col-12 col-lg-7 col-md-10 d-none d-md-block">
                        <!--=======  fitler titles  =======-->
                        <div class="filter-title filter-title--type-two">
                            <ul class="product-filter-menu">
                                <li class="ta" @click="setType($event)" data-filter="*">All</li>
                                <li class="ta active" @click="setType($event, 'cash')" data-filter=".cash">Daily Sale</li>
                                <li class="ta" @click="setType($event,'online')" data-filter=".online">Online Auctions</li>
                                <li class="ta" @click="setType($event, 'live')" data-filter=".live">Auction Listing</li>
                            </ul>
                        </div>
                        <!--=======  End of fitler titles  =======-->
                    </div>

                    <div class="col-12 col-lg-5 col-md-2">
                        <!--=======  filter icons  =======-->

                        <div class="filter-icons">
                            <!--=======  filter dropdown  =======-->

                            <div class="mr-3 filter-dropdown">
                                <input type="" @input="getItems" placeholder="Search ..." class="form-control" v-model="search" name="search">
                            </div>

                            <div class="mr-3 filter-dropdown">
                                <select class="form-control" @change="getItems" name="branch_id" v-model="branch_id">
                                    <option value="" selected> All Branches </option>
                                    <option :value="branch.id" v-text="branch.name" v-for="(branch, index) in branches">  
                                    </option>
                                </select>
                            </div>

                            <div class="filter-dropdown">
                                <select class="form-control" @change="getItems" name="auction_type_id" v-model="auction_type_id">
                                    <option value="" selected> All Categories </option>
                                    <option :value="auctionType.id" v-text="auctionType.name" v-for="(auctionType, index) in auctionTypes">  </option>
                                </select>
                            </div>

                            <!--=======  End of filter dropdown  =======-->

                            <!--=======  grid icons  =======-->

                            <!--                             
                            <div class="single-icon grid-icons">
                                <a data-target="five-column" class="active" href="javascript:void(0)"><i
                                        class="ti-layout-grid4-alt"></i></a>
                                <a data-target="four-column" href="javascript:void(0)"><i class="ti-layout-grid3-alt"></i></a>
                                <a data-target="three-column" href="javascript:void(0)"><i class="ti-layout-grid2-alt"></i></a>
                            </div> 
                            -->

                            <!--=======  End of grid icons  =======-->
                        </div>

                        <!--=======  End of filter icons  =======-->
                    </div>

                </div>
            </div>
        </div>

        <!--=======  End of shop page header  =======-->

        <!--=============================================
        =            shop page content         =
        =============================================-->

        <div class="shop-page-content mb-100">
            <div class="row">

                    <div class="col-xl-12 col-lg-12 order-1 order-lg-2 mb-md-80 mb-sm-80">

                        <div class="row product-isotope  m-4 shop-product-wrap five-column" v-if="items">

              
           

                        <!-- foreach( Facades\App\Cache\Repo::getItems() as $item) -->

                            <!--=======  single product  =======-->
                            <div :class="'col-12 col-lg-is-5 mb-4 col-md-6 col-sm-6 ' + item.type" v-for="(item, index) in items.data">
                                <div class="single-product rounded">
                                    <!--=======  single product image  =======-->

                                    <div class="single-product__image">
                                        <a class="image-wrap" href="#!" data-toggle="modal" data-target=".preview-item-modal" @click="viewPreview(item)">
                                            <img  :src="item.cropped" class="img-fluid rounded" alt="">
                                        </a>

                                        <div class="single-product__floating-badges">
                                            <span class="hot">
                                                <small v-text="item?.auction_type?.type"></small>
                                            </span>
                                        </div>
                                    </div>

                                    <!--=======  End of single product image  =======-->

                                    <!--=======  single product content  =======-->

                                    <div class="single-product__content">
                                        <div class="title">
                                            <h3> 
                                                <a href="#!" @click="viewItem(item)" v-text="item.name"></a>
                                            </h3>

                                            <a class="place-a-bid-btn" v-if="item?.auction_type?.type == 'cash'" href="#!" @click="viewItem(item)">
                                                <span>VIEW DETAILS</span>
                                            </a>
                                            <a class="place-a-bid-btn" href="#!" v-if="item?.auction_type?.type == 'online'" @click="viewItem(item)">
                                                <span>PLACE A BID</span>
                                            </a>
                                            <a class="place-a-bid-btn" href="#!" v-if="item?.auction_type?.type == 'live'" @click="viewItem(item)">
                                                <span>PLACE A BID</span>
                                            </a>

                                            <!-- data-toggle="modal" data-target=".bid-modal" data-id="$item->id "   -->
                                        </div>
                                        <div class="price">
                                            <!-- <span class="main-price discounted">$360.00</span> -->
                                            <span class="discounted-price" v-if="item?.auction_type?.type == 'cash'">
                                                Price:
                                                <span v-text="formatNumber(item.target_amount)"></span>
                                            </span>
                                            <span class="discounted-price" v-if="item?.auction_type?.type == 'online'">
                                                Current Bid:
                                                <span v-text="formatNumber(item.bid_amount)"></span>
                                            </span>            
                                            <span class="discounted-price" v-if="item?.auction_type?.type == 'live'">
                                                Current Bid:
                                                <span v-text="formatNumber(item.bid_amount)"></span>
                                            </span>
                                            <br>
                                            <span class="" v-text="item?.auction_type?.name"></span>
                                        </div>
                                    </div>

                                    <!--=======  End of single product content  =======-->
                                </div>

                            </div>
                            <!--=======  End of single product  =======-->
                        <!-- endforeach -->

                        </div>

                        <div class="row" v-if="items && items.data && items.data.length > 0 && !isLastPage">
                            <div class="col-lg-12 text-center">
                                <a class="lezada-button lezada-button--medium lezada-button--icon--left" @click="more">
                                <span class="material-symbols-outlined mr-2">
                                        keyboard_arrow_down
                                    </span> 
                                    <span>MORE</span>
                                </a>
                            </div>
                        </div>
                        
                        <div class="row" v-if="!items || !items.data || items.data.length === 0">
                            <div class="col-lg-12 text-center p-5">
                                <img class="mb-3" src="/assets/svg/illustrations/oc-error.svg" alt="No Data" style="width: 16rem;">
                                <p class="mb-0">No items to display</p>
                            </div>
                        </div>

                    </div>

            </div>
        </div>

        <!--=====  End of shop page content  ======-->
    </div>

    <!--=====  End of shop page content  ======-->



<?php $__env->stopSection(); ?>


<?php $__env->startPush('scripts'); ?>

    <script type="text/javascript">

        var per_page = <?php echo json_encode( request()->per_page ?? 0 , 15, 512) ?>;
        var auction_type_id = '';

        function getFilters() {
            var filters = "";
            filters += "&per_page=" + per_page;
            filters += "&auction_type_id=" + auction_type_id;
            return filters;
        }

        function  setAuctionType($id) {
            auction_type_id = $id;
            window.location.href = "?" + getFilters();
        }

        function more(){
            per_page = eval( Number(per_page) + 10 );
            window.location.href = "?" + getFilters();
        }



        new Vue({
            el: "#welcome-el",

            data(){
                return {
                    branches: <?php echo json_encode(\App\Models\Branch::get(), 15, 512) ?>,
                    auctionTypes: [],
                    search: <?php echo json_encode( request()->search ?? '', 15, 512) ?>,
                    type: 'cash',
                    per_page: 10,
                    auction_type_id: '',
                    branch_id: '',
                    items: null,
                    isLastPage: false
                }
            },

            methods:{

                getFilters(){
                    var filters = "per_page=" + this.per_page;
                    filters += "&type=" + this.type;
                    filters += "&search=" + this.search;
                    filters += "&auction_type_id=" + this.auction_type_id;
                    filters += "&branch_id=" + this.branch_id;
                    return filters;
                },

                getItems(){
                    $(".loader-container").fadeIn();
                    this.getAuctionTypes();
                    axios.get("/ajax-items?" + this.getFilters()).then( res =>{
                        this.items = res.data;
                        // Check if we've loaded all items
                        this.isLastPage = this.items.current_page >= this.items.last_page;
                        $(".loader-container").fadeOut();
                    });
                },

                getAuctionTypes() {
                    axios.get("/ajax-auction-types?" + this.getFilters()).then( res =>{
                        this.auctionTypes = res.data;
                    });
                },

                setType(e, type = '') {
                    this.type = type;
                    this.auction_type_id = '';
                    $('.ta').removeClass('active');
                    $(e.target).addClass('active');
                    this.getItems();
                },

                more(){
                    this.per_page = eval( Number(this.per_page) + 10 );
                    this.getItems();
                },

                formatNumber (num) {
                  if( num > 0 ) {
                  } else {
                    return num;
                  }
                  return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
                },

                viewItem(item) {
                    if( item.auction_type.type == 'cash') {
                        window.location.href = '/view-cash-item/' + item.id;
                        return;
                    }
                    window.location.href = '/view-item/' + item.id;
                },

                viewPreview(item) {
                    previewitem.setItem(item)
                },


            },

            created(){
                this.getItems();
            }
        })

    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend-layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/welcome.blade.php ENDPATH**/ ?>