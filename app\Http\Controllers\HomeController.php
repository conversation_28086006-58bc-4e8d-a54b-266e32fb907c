<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Facades\App\Cache\Repo;
use Facades\App\Libraries\AuctionHandler;
use App\Models\Transaction;
use App\Models\Auction;
use App\Models\Item;
use App\Models\Order;


class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */


    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        return view('home');
    }

    /**
     * Show the modernized admin dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function modernizedAdmin()
    {
        // Get dashboard statistics
        $stats = [
            'active_auctions' => \App\Models\AuctionType::where('type', 'live')->count(),
            'total_revenue' => array_sum(Repo::getAnnualSales()),
            'total_users' => Repo::getStaffCursor()->count() + Repo::getCustomerCursor()->count(),
            'total_items' => \App\Models\Item::count(),
        ];

        // Calculate year-over-year growth for revenue
        $currentYearSales = array_sum(Repo::getAnnualSales());

        // Temporarily set the year parameter for last year's sales
        $originalYear = request()->year;
        request()->merge(['year' => date('Y') - 1]);
        $lastYearSales = array_sum(Repo::getAnnualSales());

        // Restore original year parameter
        if ($originalYear) {
            request()->merge(['year' => $originalYear]);
        } else {
            request()->offsetUnset('year');
        }

        $revenueGrowth = $lastYearSales > 0 ? (($currentYearSales - $lastYearSales) / $lastYearSales) * 100 : 0;

        // Calculate customer growth
        $totalCustomers = Repo::getCustomerCursor()->count();
        $lastMonthCustomers = \App\Models\User::where('is_customer', true)
            ->where('created_at', '<', now()->subMonth())
            ->count();
        $customerGrowth = $lastMonthCustomers > 0 ? (($totalCustomers - $lastMonthCustomers) / $lastMonthCustomers) * 100 : 0;

        // Get recent auctions for the dashboard
        $recentAuctions = \App\Models\AuctionType::with('items')
            ->where('type', 'live')
            ->take(5)
            ->get()
            ->map(function ($auction) {
                return (object) [
                    'title' => $auction->name,
                    'status' => 'Active',
                    'current_bid' => $auction->items->sum('reserve_price') ?? 0,
                ];
            });

        // Get recent activities (real data instead of sample)
        $recentActivities = collect([
            // Recent orders
            ...\App\Models\Order::with('user')
                ->latest()
                ->take(3)
                ->get()
                ->map(function ($order) {
                    return (object) [
                        'type' => 'order',
                        'title' => 'New order placed',
                        'description' => "Order #{$order->id} by " . ($order->user->name ?? 'Unknown'),
                        'time' => $order->created_at->diffForHumans(),
                        'icon' => 'shopping-cart',
                        'color' => 'green'
                    ];
                }),
            // Recent user registrations
            ...\App\Models\User::where('is_customer', true)
                ->latest()
                ->take(2)
                ->get()
                ->map(function ($user) {
                    return (object) [
                        'type' => 'user',
                        'title' => 'New customer registered',
                        'description' => $user->name . ' joined the platform',
                        'time' => $user->created_at->diffForHumans(),
                        'icon' => 'user-plus',
                        'color' => 'blue'
                    ];
                }),
            // Recent items added
            ...\App\Models\Item::with('auctionType')
                ->latest()
                ->take(2)
                ->get()
                ->map(function ($item) {
                    return (object) [
                        'type' => 'item',
                        'title' => 'New item listed',
                        'description' => $item->name . ' added to ' . ($item->auctionType->name ?? 'auction'),
                        'time' => $item->created_at->diffForHumans(),
                        'icon' => 'package',
                        'color' => 'purple'
                    ];
                })
        ])->sortByDesc(function ($activity) {
            // Sort by most recent
            return $activity->time;
        })->take(7);

        // Get notification count (real count based on unread notifications or system alerts)
        $notificationCount = 0; // You can implement actual notification logic here

        return view('admin.modernized-dashboard', compact(
            'stats',
            'recentAuctions',
            'notificationCount',
            'revenueGrowth',
            'customerGrowth',
            'recentActivities'
        ));
    }


    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function welcome()
    {
        // Redirect authenticated non-customer users to admin dashboard
        if( auth()->check() && ! auth()->user()->is_customer ) {
            return redirect('/home');
        }

        // Handle AJAX requests for items
        if( request()->ajax() ) {
            try {
                $items = Repo::getItems();
                return response()->json($items);
            } catch (\Exception $e) {
                return response()->json(['error' => 'Failed to load items'], 500);
            }
        }

        // Load welcome page with necessary data
        try {
            $auctionTypes = \App\Models\AuctionType::with('items')->get();
            $branches = \App\Models\Branch::all();

            return view('welcome', compact('auctionTypes', 'branches'));
        } catch (\Exception $e) {
            // Fallback if there are database issues
            return view('welcome');
        }
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function shop()
    {

        if( request()->ajax() ) {
            $items = Repo::getItems();
            return response($items);
        }

        return view('welcome');
    }
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function about()
    {
        return view('frontend.about');
    }

    public function refundList(){
        $auctions = Repo::refundList();
        return view("app.reports.refund-list-report", compact("auctions"));
    }

    public function winnersReport(){
        $auctions = Repo::winnersReport();
        return view("app.reports.winners-report", compact("auctions")); 
    }
    
    public function salesReport(){
        $items = Repo::salesReport();
        return view("app.reports.sales-report", compact("items"));
    }
    
    public function inventoryReport(){
        $items = Repo::inventoryReport();
        return view("app.reports.inventory-report", compact("items"));
    }    

    public function refundListReport (){
        $transactions = Repo::refundListReport();
        return view("app.reports.refund-list-report", compact("transactions"));
    }

    public function depositsReport (){
        $transactions = Repo::depositsReport();
        return view("app.reports.deposit-report", compact("transactions"));
    }

    // Modernized report methods
    public function modernizedWinnersReport(){
        $auctions = Auction::search(request()->search)->latest();
        $auctions = Repo::applyDateFilter($auctions);
        $auctions = $auctions->whereNotNull('tagged_by')->paginate(10);
        return view("admin.modernized-winners-report", compact("auctions"));
    }

    public function modernizedSalesReport(){
        $items = Repo::getSaleCursor()
                    ->latest()
                    ->paginate(10);
        return view("admin.modernized-sales-report", compact("items"));
    }

    public function modernizedInventoryReport(){
        $items = Item::search(request()->search)->latest();
        if(request()->auction_type_id) {
            $items->where("auction_type_id", request()->auction_type_id);
        }
        $items = $items->whereNull('closed_by')->paginate(10);
        return view("admin.modernized-inventory-report", compact("items"));
    }

    public function modernizedRefundListReport (){
        $transactions = Transaction::search(request()->search)
            ->latest()
            ->whereNull('closed_by')
            ->whereNotNull('user_id')
            ->paginate(10);
        return view("admin.modernized-refund-list-report", compact("transactions"));
    }

    public function modernizedDepositsReport (){
        $transactions = Transaction::search(request()->search)->latest();
        $transactions = Repo::applyDateFilter($transactions);
        if(request()->user_id) {
            $transactions->where("created_by", request()->user_id);
        }
        $transactions = $transactions->whereNull('closed_by')->paginate(10);
        return view("admin.modernized-deposits-report", compact("transactions"));
    }

    // Modernized report detail methods
    public function modernizedWinnersReportDetail(\App\Models\Auction $auction){
        return view("admin.modernized-winners-report-detail", compact("auction"));
    }

    public function modernizedSalesReportDetail(\App\Models\Order $order){
        return view("admin.modernized-sales-report-detail", compact("order"));
    }

    public function modernizedInventoryReportDetail(\App\Models\Item $item){
        return view("admin.modernized-inventory-report-detail", compact("item"));
    }

    public function modernizedRefundListReportDetail(\App\Models\Transaction $transaction){
        return view("admin.modernized-refund-list-report-detail", compact("transaction"));
    }

    public function modernizedDepositsReportDetail (\App\Models\Transaction $transaction){
        return view("admin.modernized-deposits-report-detail", compact("transaction"));
    }

    public function notifications(Request $request) {
        return view("app.notifications.index");
    }

    // Route::get('refund-list-report', [HomeController::class, 'refundListReport']);
    // Route::get('winners-report', [HomeController::class, 'winnersReport']);
    // Route::get('sales-report', [HomeController::class, 'salesReport']);
    // Route::get('inventory-report', [HomeController::class, 'inventoryReport']);
    // Route::get('deposits-report', [HomeController::class, 'depositsReport']);




}
