<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\AdvertController;
use App\Http\Controllers\StatusController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\AuctionController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\AuctionTypeController;
use App\Http\Controllers\AuctionListingController;
use App\Http\Controllers\TransactionController;

use App\Http\Controllers\DataVerseController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ZoomController;

use Spatie\Permission\Models\Permission;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\SupplierController;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use App\Http\Controllers\Auth\VerificationController;
use Illuminate\Http\Request;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Test route for auction components
Route::get('/test', function () {
    $data = \App\Models\Item::paginate();
    return view('test', compact('data'));
});


Auth::routes();

// Test route for auction components
Route::get('/test-auction-components', function () {
    return view('test-auction-components');
})->name('test.auction.components');

// Simple test route for auction components
Route::get('/test-auction-simple', function () {
    return view('test-auction-simple');
})->name('test.auction.simple');

Route::get('/email/verify', function () {
    return view('auth.verify');
})->middleware('auth')->name('verification.notice');


Route::post('/email/verification-notification', function (Request $request) {
    $request->user()->sendEmailVerificationNotification();

    return back()->with('message', 'Verification link sent!');
})->middleware(['auth', 'throttle:6,1'])->name('verification.send');

// Route::get('email/verify', [VerificationController::class, 'show'])->name('verification.notice');
Route::get('/email/verify/{id}/{hash}', [VerificationController::class, 'verify'])->name('verification.verify');
// Route::get('email/verify/{id}', [VerificationController::class, 'verify'])->name('verification.verify');
Route::get('email/resend', [VerificationController::class, 'resend'])->name('verification.resend.get');
Route::post('email/resend', [VerificationController::class, 'resend'])->name('verification.resend');

// Route::get('/', [HomeController::class, 'welcome']);

Route::get('/', function () {
    return view('spa');
})->name('spa.homepage');

Route::get('/zoom', [ZoomController::class, 'zoom']);
Route::get('/zoom-auth', [ZoomController::class, 'zoomAuth']);

Route::get('/trust-policy', function(){
    return view("zoom-policy");
});

Route::get('/login', function () {
    return redirect('/');
})->name("login");

// Custom login POST route using the custom LoginController
Route::post('/login', [App\Http\Controllers\Auth\LoginController::class, 'login']);

Route::controller(PaymentController::class)
->prefix('paypal')
->group(function () {
    Route::view('payment', 'paypal.index')->name('create.payment');
    Route::get('handle-payment', 'handlePayment')->name('make.payment');
    Route::get('cancel-payment', 'paymentCancel')->name('cancel.payment');
    Route::get('payment-success', 'paymentSuccess')->name('success.payment');
    Route::get('payment-notify', 'paymentNotify')->name('notify.payment');
});

// DPO Pay Routes with rate limiting and security
Route::controller(PaymentController::class)
->prefix('dpopay')
->middleware(['web', \App\Http\Middleware\DPOPayRateLimit::class])
->group(function () {
    Route::view('payment', 'dpopay.index')->name('create.dpopay.payment');
    Route::view('test', 'dpopay.test')->name('dpopay.test'); // Test page with production URLs

    // Test routes for success/failure pages (remove in production)
    Route::get('test-success', function() {
        $payment = new \App\Models\DumpPayment([
            'amount' => 450.00,
            'currency' => 'USD',
            'company_ref' => 'VERTIGO-********-E1500A9C',
            'customer_email' => '<EMAIL>',
            'payment_date' => now(),
            'status' => 'completed'
        ]);

        return view('dpopay.success', [
            'payment' => $payment,
            'transaction_id' => 'F2B56B76-5AB9-428D-8E50-FD1E8A8D58D2',
            'approval_code' => '**********',
            'reference' => 'VERTIGO-********-E1500A9C',
            'amount' => 450.00,
            'currency' => 'USD'
        ]);
    })->name('dpopay.test.success');

    Route::get('test-failure', function() {
        return view('dpopay.failure', [
            'error' => 'Payment was declined by your bank.',
            'details' => 'Your card was declined. Please try a different payment method.',
            'can_retry' => true
        ]);
    })->name('dpopay.test.failure');
    Route::get('handle-payment', 'handleDPOPayment')->name('make.dpopay.payment');
    Route::post('checkout-payment', 'handleCheckoutDPOPayment')->name('checkout.dpopay.payment');
    Route::get('cancel-payment', 'dpoPaymentCancel')->name('dpopay.cancel');
    Route::get('payment-success', 'dpoPaymentSuccess')->name('dpopay.success');
    Route::get('payment-failure', 'dpoPaymentCancel')->name('dpopay.failure'); // Alias for failure

    // Webhook endpoint with CSRF exemption (handled separately)
    Route::post('payment-notify', 'dpoPaymentNotify')
        ->name('dpopay.notify')
        ->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]);
});



// Route::get('/email/verify', function () {
//     return view('auth.verify');
// })->middleware('auth')->name('verification.notice');

// Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
//     $request->fulfill();

//     return redirect('/home');
// })->middleware(['auth', 'signed'])->name('verification.verify');

// Route::post('/email/verification-notification', function (Request $request) {
//     $request->user()->sendEmailVerificationNotification();

//     return back()->with('message', 'Verification link sent!');
// })->middleware(['auth', 'throttle:6,1'])->name('verification.send');

Route::get('/fix', function() {


    \App\Models\Status::create(['id' => 20, 'name' => 'Client owned']);
    \App\Models\Status::create(['id' => 21, 'name' => 'Trust owned']);
    \App\Models\Status::create(['id' => 22, 'name' => 'Suspense owned']);

    // Permission::create(['name' => 'sales report']);
    // Permission::create(['name' => 'inventory report']);
    // Permission::create(['name' => 'refund list report']);
    // Permission::create(['name' => 'deposits report']);
    // Permission::create(['name' => 'winners report']);

    // Permission::create(['name' => 'list orders']);
    // Permission::create(['name' => 'view orders']);
    // Permission::create(['name' => 'create orders']);
    // Permission::create(['name' => 'update orders']);
    // Permission::create(['name' => 'delete orders']);

    // Permission::create(['name' => 'list sales']);
    // Permission::create(['name' => 'view sales']);
    // Permission::create(['name' => 'create sales']);
    // Permission::create(['name' => 'update sales']);
    // Permission::create(['name' => 'delete sales']);

    echo "Done";
});

Route::resource('dataverses', DataVerseController::class);
Route::get('/dataverses.auth',[ DataVerseController::class, 'dataversesAuth'])->name("dataverses.auth");

Route::get('/shop', [HomeController::class, 'shop'])->name('shop');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/admin-login', function(){
    return view('auth.admin-login');
})->name('admin-login');

// Public cart routes (no authentication required)
Route::get('/view-item/{item}', [ItemController::class, 'viewItem']);
Route::get('/view-cash-item/{item}', [ItemController::class, 'viewCashItem']);
Route::get('/add-to-cart/{item}', [ItemController::class, 'addToCart']);
Route::get('/remove-from-cart/{item}', [ItemController::class, 'removeFromCart']);

// Cart API routes (using web middleware for session support)
Route::prefix('api')->group(function () {
    // Cart routes (public for now, can be moved to auth middleware later)
    Route::get('/cart', function() {
        $items = \Facades\App\Cache\Repo::cart();
        return response()->json($items);
    });

    // Add item to cart API
    Route::post('/cart/add/{item}', function(\App\Models\Item $item) {
        try {
            $type = $item->auctionType->type ?? '';
            if (!$type) {
                return response()->json(['success' => false, 'message' => 'Invalid item type'], 400);
            }

            // Get current cart items and quantities
            $items = session($type) ?? [];
            $quantities = session($type . '_quantities') ?? [];
            $quantity = request()->input('quantity', 1);

            // Validate using CartValidator
            $validation = \App\Validators\CartValidator::validateAddToCart($item, $quantity, $items);
            if (!$validation['valid']) {
                return response()->json(['success' => false, 'message' => $validation['message']], 400);
            }

            // Add item to cart if not already present
            if (!in_array($item->id, $items)) {
                $items[] = $item->id;
                $quantities[$item->id] = $quantity;
            } else {
                // Update quantity if item already in cart
                $quantities[$item->id] = ($quantities[$item->id] ?? 1) + $quantity;
            }

            // Save to session
            session([$type => $items]);
            session([$type . '_quantities' => $quantities]);

            return response()->json([
                'success' => true,
                'message' => $item->name . ' added to cart',
                'quantity' => $quantities[$item->id],
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to add item to cart'], 500);
        }
    });

    // Remove item from cart API
    Route::delete('/cart/remove/{item}', function(\App\Models\Item $item) {
        try {
            $type = $item->auctionType->type ?? '';
            if (!$type) {
                return response()->json(['success' => false, 'message' => 'Invalid item type'], 400);
            }

            // Get current cart items and quantities
            $items = session($type) ?? [];
            $quantities = session($type . '_quantities') ?? [];
            $quantityToRemove = request()->input('quantity', null);

            // If no specific quantity provided, remove entire item
            if ($quantityToRemove === null) {
                $items = array_diff($items, [$item->id]);
                unset($quantities[$item->id]);
                $message = $item->name . ' removed from cart';
            } else {
                // Reduce quantity
                $currentQuantity = $quantities[$item->id] ?? 1;
                $newQuantity = $currentQuantity - $quantityToRemove;

                if ($newQuantity <= 0) {
                    // Remove item completely if quantity becomes 0 or negative
                    $items = array_diff($items, [$item->id]);
                    unset($quantities[$item->id]);
                    $message = $item->name . ' removed from cart';
                } else {
                    $quantities[$item->id] = $newQuantity;
                    $message = $item->name . ' quantity reduced to ' . $newQuantity;
                }
            }

            // Save to session
            session([$type => $items]);
            session([$type . '_quantities' => $quantities]);

            return response()->json([
                'success' => true,
                'message' => $message,
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to remove item from cart'], 500);
        }
    });

    // Update cart item quantity API
    Route::patch('/cart/update/{item}', function(\App\Models\Item $item) {
        try {
            $type = $item->auctionType->type ?? '';
            if (!$type) {
                return response()->json(['success' => false, 'message' => 'Invalid item type'], 400);
            }

            $quantity = request()->input('quantity');
            if ($quantity === null) {
                return response()->json(['success' => false, 'message' => 'Quantity parameter required'], 400);
            }

            // Validate quantity (allow 0 for removal)
            if (!is_numeric($quantity) || $quantity < 0) {
                return response()->json(['success' => false, 'message' => 'Invalid quantity'], 400);
            }

            // Get current cart items and quantities
            $items = session($type) ?? [];
            $quantities = session($type . '_quantities') ?? [];

            // Check if item is in cart
            if (!in_array($item->id, $items)) {
                return response()->json(['success' => false, 'message' => 'Item not in cart'], 404);
            }

            // Validate item availability (except when removing)
            if ($quantity > 0) {
                $itemValidation = \App\Validators\CartValidator::validateItemForCart($item);
                if (!$itemValidation['valid']) {
                    return response()->json(['success' => false, 'message' => $itemValidation['message']], 400);
                }

                $quantityValidation = \App\Validators\CartValidator::validateQuantity($quantity);
                if (!$quantityValidation['valid']) {
                    return response()->json(['success' => false, 'message' => $quantityValidation['message']], 400);
                }
            }

            if ($quantity == 0) {
                // Remove item completely
                $items = array_diff($items, [$item->id]);
                unset($quantities[$item->id]);
                $message = $item->name . ' removed from cart';
            } else {
                // Update quantity
                $quantities[$item->id] = (int)$quantity;
                $message = $item->name . ' quantity updated to ' . $quantity;
            }

            // Save to session
            session([$type => $items]);
            session([$type . '_quantities' => $quantities]);

            return response()->json([
                'success' => true,
                'message' => $message,
                'quantity' => $quantity == 0 ? 0 : $quantities[$item->id] ?? 0,
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update cart item'], 500);
        }
    });

    // Batch add multiple items to cart API
    Route::post('/cart/add-multiple', function() {
        try {
            $itemIds = request()->input('items', []);
            $defaultQuantity = request()->input('default_quantity', 1);

            if (empty($itemIds) || !is_array($itemIds)) {
                return response()->json(['success' => false, 'message' => 'Items array required'], 400);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($itemIds as $itemData) {
                try {
                    // Handle both simple ID arrays and objects with quantity
                    if (is_array($itemData)) {
                        $itemId = $itemData['id'] ?? null;
                        $quantity = $itemData['quantity'] ?? $defaultQuantity;
                    } else {
                        $itemId = $itemData;
                        $quantity = $defaultQuantity;
                    }

                    if (!$itemId) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Invalid item ID'];
                        $failureCount++;
                        continue;
                    }

                    $item = \App\Models\Item::find($itemId);
                    if (!$item) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Item not found'];
                        $failureCount++;
                        continue;
                    }

                    $type = $item->auctionType->type ?? '';
                    if (!$type) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Invalid item type'];
                        $failureCount++;
                        continue;
                    }

                    // Add item to cart
                    $items = session($type) ?? [];
                    $quantities = session($type . '_quantities') ?? [];

                    if (!in_array($item->id, $items)) {
                        $items[] = $item->id;
                        $quantities[$item->id] = $quantity;
                    } else {
                        $quantities[$item->id] = ($quantities[$item->id] ?? 1) + $quantity;
                    }

                    session([$type => $items]);
                    session([$type . '_quantities' => $quantities]);

                    $results[] = [
                        'id' => $itemId,
                        'success' => true,
                        'message' => $item->name . ' added',
                        'quantity' => $quantities[$item->id]
                    ];
                    $successCount++;

                } catch (\Exception $e) {
                    $results[] = ['id' => $itemId ?? 'unknown', 'success' => false, 'message' => 'Error adding item'];
                    $failureCount++;
                }
            }

            return response()->json([
                'success' => $failureCount === 0,
                'message' => "Added {$successCount} items, {$failureCount} failed",
                'results' => $results,
                'summary' => [
                    'total' => count($itemIds),
                    'success' => $successCount,
                    'failed' => $failureCount
                ],
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to add items to cart'], 500);
        }
    });

    // Batch remove multiple items from cart API
    Route::delete('/cart/remove-multiple', function() {
        try {
            $itemIds = request()->input('items', []);

            if (empty($itemIds) || !is_array($itemIds)) {
                return response()->json(['success' => false, 'message' => 'Items array required'], 400);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($itemIds as $itemId) {
                try {
                    $item = \App\Models\Item::find($itemId);
                    if (!$item) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Item not found'];
                        $failureCount++;
                        continue;
                    }

                    $type = $item->auctionType->type ?? '';
                    if (!$type) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Invalid item type'];
                        $failureCount++;
                        continue;
                    }

                    // Remove item from cart
                    $items = session($type) ?? [];
                    $quantities = session($type . '_quantities') ?? [];

                    if (in_array($item->id, $items)) {
                        $items = array_diff($items, [$item->id]);
                        unset($quantities[$item->id]);

                        session([$type => $items]);
                        session([$type . '_quantities' => $quantities]);

                        $results[] = ['id' => $itemId, 'success' => true, 'message' => $item->name . ' removed'];
                        $successCount++;
                    } else {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Item not in cart'];
                        $failureCount++;
                    }

                } catch (\Exception $e) {
                    $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Error removing item'];
                    $failureCount++;
                }
            }

            return response()->json([
                'success' => $failureCount === 0,
                'message' => "Removed {$successCount} items, {$failureCount} failed",
                'results' => $results,
                'summary' => [
                    'total' => count($itemIds),
                    'success' => $successCount,
                    'failed' => $failureCount
                ],
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to remove items from cart'], 500);
        }
    });
});
Route::get('/cart', function () {
    return view('spa');
})->name('cart');
Route::get('/checkout', function () {
    return view('spa');
})->name('checkout');
Route::post('/checkout', [ItemController::class, 'checkoutSave']);

// Payment success page (Vue component)
Route::get('/payment-success', function () {
    return view('spa');
})->name('payment.success');

// Test cart clearing (for debugging)
Route::get('/test-clear-cart', function () {
    try {
        // Get current cart before clearing
        $cartBefore = \Facades\App\Cache\Repo::cart();

        // Clear cart sessions
        session()->forget(['cash', 'online', 'live']);

        // Get cart after clearing
        $cartAfter = \Facades\App\Cache\Repo::cart();

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully',
            'cart_before' => [
                'count' => $cartBefore->count(),
                'items' => $cartBefore->pluck('id', 'name')->toArray()
            ],
            'cart_after' => [
                'count' => $cartAfter->count(),
                'items' => $cartAfter->pluck('id', 'name')->toArray()
            ],
            'sessions_before' => [
                'cash' => session('cash') ?? [],
                'online' => session('online') ?? [],
                'live' => session('live') ?? []
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});

// Test order completion and cart clearing
Route::get('/test-order-completion', function () {
    try {
        // Create a dummy payment record for testing
        $payment = new \App\Models\DumpPayment();
        $payment->id = 999; // Dummy ID
        $payment->company_ref = 'TEST-' . time();
        $payment->amount = 25000;
        $payment->currency = 'MWK';
        $payment->status = 'pending';
        $payment->data = json_encode(['test' => 'data']);

        // Get cart before clearing
        $cartBefore = \Facades\App\Cache\Repo::cart();

        // Call the order completion service
        $result = \App\Services\OrderCompletionService::completeOrder($payment, [
            'test_mode' => true,
            'trans_id' => 'TEST-123',
            'approval_code' => 'APP-456'
        ]);

        // Get cart after clearing
        $cartAfter = \Facades\App\Cache\Repo::cart();

        return response()->json([
            'success' => true,
            'message' => 'Order completion test completed',
            'completion_result' => $result,
            'cart_before' => [
                'count' => $cartBefore->count(),
                'items' => $cartBefore->pluck('id', 'name')->toArray()
            ],
            'cart_after' => [
                'count' => $cartAfter->count(),
                'items' => $cartAfter->pluck('id', 'name')->toArray()
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

Route::prefix('/')->middleware(['auth', 'verified'])->group(function () {

    Route::post('/cancel-bid/{auction}', [AuctionController::class, 'cancelBid']);
    Route::get('/cancel-bid/{auction}', [AuctionController::class, 'cancelBid']);
    Route::get('/profile', [UserController::class, 'profile'])->name('profile');
    Route::post('/profile', [UserController::class, 'saveProfile'])->name('profile.save');
    Route::post('/place-a-bid/{item}', [ AuctionController::class, 'placeABid' ]);
    Route::post('/ajax-bid', [ AuctionController::class, 'ajaxBid' ]);
    Route::get('/register-bid', [ AuctionController::class, 'registerBid' ]);
    Route::post('/register-bid', [ AuctionController::class, 'registerBidSave' ]);
    Route::get('/ajax-auction/{auction}', [ AuctionController::class, 'ajaxAuction' ]);

    Route::middleware('staffMiddleware')->group(function () {

        Route::get('/home', [HomeController::class, 'modernizedAdmin'])->name('admin.index');
        Route::get('/admin', [HomeController::class, 'modernizedAdmin'])->name('admin.index');

        // Backward compatibility route for users.index
        Route::get('/users', function() {
            return redirect()->route('admin.users.index');
        })->name('users.index');


        // V2 (Main) sales/orders routes
        Route::get('/orders', [OrderController::class, 'modernizedIndex'])->name('orders.index');
        Route::get('/orders/create', [OrderController::class, 'modernizedCreate'])->name('orders.create');
        Route::get('/orders/{order}', [OrderController::class, 'modernizedShow'])->name('orders.show');
        Route::get('/orders/{order}/edit', [OrderController::class, 'modernizedEdit'])->name('orders.edit');
        Route::delete('/orders-bulk-delete', [OrderController::class, 'bulkDelete'])->name('orders.bulk-delete');
        Route::post('/orders-bulk-export', [OrderController::class, 'bulkExport'])->name('orders.bulk-export');

        // V1 (Legacy) sales/orders routes for backward compatibility
        Route::prefix('v1')->group(function () {
            Route::resource('orders', OrderController::class)->names([
                'index' => 'v1.orders.index',
                'create' => 'v1.orders.create',
                'store' => 'v1.orders.store',
                'show' => 'v1.orders.show',
                'edit' => 'v1.orders.edit',
                'update' => 'v1.orders.update',
                'destroy' => 'v1.orders.destroy'
            ]);
            Route::get('/ajax-order/{order}', [OrderController::class, 'ajaxOrder'])->name('v1.ajax-order');
            Route::post('/sale-bid', [OrderController::class, 'saleBid'])->name('v1.sale-bid');
        });

        // V2 (Main) auctions routes
        Route::get('/auctions', [AuctionController::class, 'modernizedIndex'])->name('auctions.index');
        Route::get('/auctions/create', [AuctionController::class, 'modernizedCreate'])->name('auctions.create');
        Route::post('/auctions', [AuctionController::class, 'modernizedStore'])->name('auctions.store');
        Route::get('/auctions/{auction}', [AuctionController::class, 'modernizedShow'])->name('auctions.show');
        Route::get('/auctions/{auction}/edit', [AuctionController::class, 'modernizedEdit'])->name('auctions.edit');
        Route::put('/auctions/{auction}', [AuctionController::class, 'modernizedUpdate'])->name('auctions.update');
        Route::delete('/auctions/{auction}', [AuctionController::class, 'modernizedDestroy'])->name('auctions.destroy');

        // V2 (Main) auction types routes
        Route::get('/auction-types', [AuctionTypeController::class, 'modernizedIndex'])->name('auction-types.index');
        Route::get('/auction-types/create', [AuctionTypeController::class, 'modernizedCreate'])->name('auction-types.create');
        Route::post('/auction-types', [AuctionTypeController::class, 'modernizedStore'])->name('auction-types.store');
        Route::get('/auction-types/{auctionType}', [AuctionTypeController::class, 'modernizedShow'])->name('auction-types.show');
        Route::get('/auction-types/{auctionType}/edit', [AuctionTypeController::class, 'modernizedEdit'])->name('auction-types.edit');
        Route::put('/auction-types/{auctionType}', [AuctionTypeController::class, 'modernizedUpdate'])->name('auction-types.update');
        Route::delete('/auction-types/{auctionType}', [AuctionTypeController::class, 'modernizedDestroy'])->name('auction-types.destroy');

        // V2 (Main) auction listing routes
        Route::get('/auction-listing/create', [AuctionListingController::class, 'modernizedCreate'])->name('auction-listing.create');

        // V2 (Main) items routes
        Route::get('/items', [ItemController::class, 'modernizedIndex'])->name('items.index');
        Route::get('/items/create', [ItemController::class, 'modernizedCreate'])->name('items.create');
        Route::post('/items', [ItemController::class, 'modernizedStore'])->name('items.store');
        Route::get('/items/{item}', [ItemController::class, 'modernizedShow'])->name('items.show');
        Route::get('/items/{item}/edit', [ItemController::class, 'modernizedEdit'])->name('items.edit');
        Route::put('/items/{item}', [ItemController::class, 'modernizedUpdate'])->name('items.update');
        Route::delete('/items/{item}', [ItemController::class, 'modernizedDestroy'])->name('items.destroy');

        // V1 (Legacy) routes for backward compatibility
        Route::prefix('v1')->group(function () {
            Route::resource('auctions', AuctionController::class)->names([
                'index' => 'v1.auctions.index',
                'create' => 'v1.auctions.create',
                'store' => 'v1.auctions.store',
                'show' => 'v1.auctions.show',
                'edit' => 'v1.auctions.edit',
                'update' => 'v1.auctions.update',
                'destroy' => 'v1.auctions.destroy'
            ]);
            Route::resource('auction-types', AuctionTypeController::class)->names([
                'index' => 'v1.auction-types.index',
                'create' => 'v1.auction-types.create',
                'store' => 'v1.auction-types.store',
                'show' => 'v1.auction-types.show',
                'edit' => 'v1.auction-types.edit',
                'update' => 'v1.auction-types.update',
                'destroy' => 'v1.auction-types.destroy'
            ]);
            Route::resource('items', ItemController::class)->names([
                'index' => 'v1.items.index',
                'create' => 'v1.items.create',
                'store' => 'v1.items.store',
                'show' => 'v1.items.show',
                'edit' => 'v1.items.edit',
                'update' => 'v1.items.update',
                'destroy' => 'v1.items.destroy'
            ]);
        });

        // V2 (Main) admin users routes
        Route::get('/admin/users', [UserController::class, 'modernizedIndex'])->name('admin.users.index');
        Route::get('/admin/users/create', [UserController::class, 'modernizedCreate'])->name('admin.users.create');
        Route::post('/admin/users', [UserController::class, 'modernizedStore'])->name('admin.users.store');
        Route::get('/admin/users/{user}', [UserController::class, 'modernizedShow'])->name('admin.users.show');
        Route::get('/admin/users/{user}/edit', [UserController::class, 'modernizedEdit'])->name('admin.users.edit');
        Route::put('/admin/users/{user}', [UserController::class, 'modernizedUpdate'])->name('admin.users.update');
        Route::delete('/admin/users/{user}', [UserController::class, 'modernizedDestroy'])->name('admin.users.destroy');

        // V2 (Main) admin roles routes
        Route::get('/admin/roles', [RoleController::class, 'modernizedIndex'])->name('admin.roles.index');
        Route::get('/admin/roles/create', [RoleController::class, 'modernizedCreate'])->name('admin.roles.create');
        Route::post('/admin/roles', [RoleController::class, 'modernizedStore'])->name('admin.roles.store');
        Route::get('/admin/roles/{role}', [RoleController::class, 'modernizedShow'])->name('admin.roles.show');
        Route::get('/admin/roles/{role}/edit', [RoleController::class, 'modernizedEdit'])->name('admin.roles.edit');
        Route::put('/admin/roles/{role}', [RoleController::class, 'modernizedUpdate'])->name('admin.roles.update');
        Route::delete('/admin/roles/{role}', [RoleController::class, 'modernizedDestroy'])->name('admin.roles.destroy');

        // V2 (Main) admin statuses routes
        Route::get('/admin/statuses', [StatusController::class, 'modernizedIndex'])->name('admin.statuses.index');
        Route::get('/admin/statuses/create', [StatusController::class, 'modernizedCreate'])->name('admin.statuses.create');
        Route::post('/admin/statuses', [StatusController::class, 'modernizedStore'])->name('admin.statuses.store');
        Route::get('/admin/statuses/{status}', [StatusController::class, 'modernizedShow'])->name('admin.statuses.show');
        Route::get('/admin/statuses/{status}/edit', [StatusController::class, 'modernizedEdit'])->name('admin.statuses.edit');
        Route::put('/admin/statuses/{status}', [StatusController::class, 'modernizedUpdate'])->name('admin.statuses.update');
        Route::delete('/admin/statuses/{status}', [StatusController::class, 'modernizedDestroy'])->name('admin.statuses.destroy');

        // V2 (Main) admin settings routes
        Route::get('/admin/settings', [SettingController::class, 'modernizedIndex'])->name('admin.settings.index');
        Route::post('/admin/settings', [SettingController::class, 'modernizedStore'])->name('admin.settings.store');
        Route::delete('/admin/settings/{id}', [SettingController::class, 'modernizedDestroy'])->name('admin.settings.destroy');

        // V1 (Legacy) admin routes for backward compatibility
        Route::prefix('v1')->group(function () {
            Route::resource('users', UserController::class)->names([
                'index' => 'v1.users.index',
                'create' => 'v1.users.create',
                'store' => 'v1.users.store',
                'show' => 'v1.users.show',
                'edit' => 'v1.users.edit',
                'update' => 'v1.users.update',
                'destroy' => 'v1.users.destroy'
            ]);
            Route::resource('roles', RoleController::class)->names([
                'index' => 'v1.roles.index',
                'create' => 'v1.roles.create',
                'store' => 'v1.roles.store',
                'show' => 'v1.roles.show',
                'edit' => 'v1.roles.edit',
                'update' => 'v1.roles.update',
                'destroy' => 'v1.roles.destroy'
            ]);
            Route::resource('statuses', StatusController::class)->names([
                'index' => 'v1.statuses.index',
                'create' => 'v1.statuses.create',
                'store' => 'v1.statuses.store',
                'show' => 'v1.statuses.show',
                'edit' => 'v1.statuses.edit',
                'update' => 'v1.statuses.update',
                'destroy' => 'v1.statuses.destroy'
            ]);
            Route::resource('settings', SettingController::class)->names([
                'index' => 'v1.settings.index',
                'create' => 'v1.settings.create',
                'store' => 'v1.settings.store',
                'show' => 'v1.settings.show',
                'edit' => 'v1.settings.edit',
                'update' => 'v1.settings.update',
                'destroy' => 'v1.settings.destroy'
            ]);
            Route::resource('branches', BranchController::class)->names([
                'index' => 'v1.branches.index',
                'create' => 'v1.branches.create',
                'store' => 'v1.branches.store',
                'show' => 'v1.branches.show',
                'edit' => 'v1.branches.edit',
                'update' => 'v1.branches.update',
                'destroy' => 'v1.branches.destroy'
            ]);
        });

        // V2 (Main) branches routes
        Route::resource('branches', BranchController::class);

        // V2 (Main) other routes
        Route::resource('permissions', PermissionController::class);
        Route::resource('accounts', AccountController::class);
        Route::resource('adverts', AdvertController::class);
        Route::resource('/suppliers', SupplierController::class);
        Route::resource('transactions', TransactionController::class);

        Route::get('/accept-bid/{auction}', [ AuctionController::class, 'acceptBid']);
        Route::post('/accept-bid/{auction}', [ AuctionController::class, 'acceptBid']);
        Route::get('/set-branch/{branch}', [UserController::class, 'setBranch']);
        Route::post('/delete-media/{media}', [UserController::class, 'deleteMedia']);
        Route::get('/delete-media-get/{media}', [UserController::class, 'deleteMediaGet']);
        Route::post('/import-bank-statement', [TransactionController::class, 'importStatement'])->name('transactions.importStatement');
        Route::get('/notifications', [HomeController::class, "notifications"])->name("notifications");

        // Additional V1 (Legacy) routes for backward compatibility
        Route::prefix('v1')->group(function () {
            Route::resource('permissions', PermissionController::class)->names([
                'index' => 'v1.permissions.index',
                'create' => 'v1.permissions.create',
                'store' => 'v1.permissions.store',
                'show' => 'v1.permissions.show',
                'edit' => 'v1.permissions.edit',
                'update' => 'v1.permissions.update',
                'destroy' => 'v1.permissions.destroy'
            ]);
            Route::resource('accounts', AccountController::class)->names([
                'index' => 'v1.accounts.index',
                'create' => 'v1.accounts.create',
                'store' => 'v1.accounts.store',
                'show' => 'v1.accounts.show',
                'edit' => 'v1.accounts.edit',
                'update' => 'v1.accounts.update',
                'destroy' => 'v1.accounts.destroy'
            ]);
            Route::resource('adverts', AdvertController::class)->names([
                'index' => 'v1.adverts.index',
                'create' => 'v1.adverts.create',
                'store' => 'v1.adverts.store',
                'show' => 'v1.adverts.show',
                'edit' => 'v1.adverts.edit',
                'update' => 'v1.adverts.update',
                'destroy' => 'v1.adverts.destroy'
            ]);
            Route::resource('auction-listing', AuctionListingController::class)->names([
                'index' => 'v1.auction-listing.index',
                'create' => 'v1.auction-listing.create',
                'store' => 'v1.auction-listing.store',
                'show' => 'v1.auction-listing.show',
                'edit' => 'v1.auction-listing.edit',
                'update' => 'v1.auction-listing.update',
                'destroy' => 'v1.auction-listing.destroy'
            ]);
            Route::resource('/suppliers', SupplierController::class)->names([
                'index' => 'v1.suppliers.index',
                'create' => 'v1.suppliers.create',
                'store' => 'v1.suppliers.store',
                'show' => 'v1.suppliers.show',
                'edit' => 'v1.suppliers.edit',
                'update' => 'v1.suppliers.update',
                'destroy' => 'v1.suppliers.destroy'
            ]);
            Route::resource('transactions', TransactionController::class)->names([
                'index' => 'v1.transactions.index',
                'create' => 'v1.transactions.create',
                'store' => 'v1.transactions.store',
                'show' => 'v1.transactions.show',
                'edit' => 'v1.transactions.edit',
                'update' => 'v1.transactions.update',
                'destroy' => 'v1.transactions.destroy'
            ]);
        });

        Route::get('refund-list-report', [HomeController::class, 'refundListReport']);
        Route::get('winners-report', [HomeController::class, 'winnersReport']);
        Route::get('sales-report', [HomeController::class, 'salesReport']);
        // V2 (Main) report routes
        Route::get('winners-report', [HomeController::class, 'modernizedWinnersReport'])->name('reports.winners');
        Route::get('sales-report', [HomeController::class, 'modernizedSalesReport'])->name('reports.sales');
        Route::get('inventory-report', [HomeController::class, 'modernizedInventoryReport'])->name('reports.inventory');
        Route::get('refund-list-report', [HomeController::class, 'modernizedRefundListReport'])->name('reports.refund-list');
        Route::get('deposits-report', [HomeController::class, 'modernizedDepositsReport'])->name('reports.deposits');

        // V2 (Main) report detail routes
        Route::get('winners-report/{auction}', [HomeController::class, 'modernizedWinnersReportDetail'])->name('reports.winners.detail');
        Route::get('sales-report/{order}', [HomeController::class, 'modernizedSalesReportDetail'])->name('reports.sales.detail');
        Route::get('inventory-report/{item}', [HomeController::class, 'modernizedInventoryReportDetail'])->name('reports.inventory.detail');
        Route::get('refund-list-report/{transaction}', [HomeController::class, 'modernizedRefundListReportDetail'])->name('reports.refund-list.detail');
        Route::get('deposits-report/{transaction}', [HomeController::class, 'modernizedDepositsReportDetail'])->name('reports.deposits.detail');

        // V1 (Legacy) report routes for backward compatibility
        Route::prefix('v1')->group(function () {
            Route::get('winners-report', [HomeController::class, 'winnersReport'])->name('v1.reports.winners');
            Route::get('sales-report', [HomeController::class, 'salesReport'])->name('v1.reports.sales');
            Route::get('inventory-report', [HomeController::class, 'inventoryReport'])->name('v1.reports.inventory');
            Route::get('refund-list-report', [HomeController::class, 'refundListReport'])->name('v1.reports.refund-list');
            Route::get('deposits-report', [HomeController::class, 'depositsReport'])->name('v1.reports.deposits');
        });
    });

});

Route::get('/ajax-items', [ItemController::class, "ajaxItems"]);
Route::get('/ajax-auction-types', [AuctionTypeController::class, "ajaxAuctionTypes"]);
Route::get('/ajax-branches', [BranchController::class, "ajaxBranches"]);
Route::get('/ajax-item/{item}', [ItemController::class, "ajaxItem"]);

// Test route for Vue components
Route::get('/test-vue', function () {
    return view('test-vue');
});

// Test route for SPA homepage
Route::get('/spa', function () {
    return view('spa');
})->name('spa.homepage');

// New Vue 3 homepage (alternative to current homepage)
Route::get('/home-vue', function () {
    return view('spa');
})->name('homepage.vue');

// Vue 3 item detail page
Route::get('/item/{item}', function () {
    return view('spa');
})->name('item.detail.vue');

// SPA register-bid page
Route::get('/spa/register-bid', function () {
    return view('spa');
})->name('spa.register-bid')->middleware(['auth', 'verified']);

// SPA bid-dashboard page
Route::get('/spa/bid-dashboard', function () {
    return view('spa');
})->name('spa.bid-dashboard')->middleware(['auth', 'verified']);

// Bid dashboard page (both SPA and regular)
Route::get('/bid-dashboard', function () {
    return view('spa');
})->name('bid-dashboard')->middleware(['auth', 'verified']);

// Session-based user route for SPA authentication check
Route::get('/api/user-session', function () {
    if (auth()->check()) {
        return response()->json(auth()->user());
    }
    return response()->json(['authenticated' => false], 401);
})->name('api.user.session');

// Session-based live auctions endpoint for SPA
Route::middleware('auth')->get('/api/live-auctions-session', [App\Http\Controllers\Api\AuctionController::class, 'liveAuctions'])->name('api.live.auctions.session');

// Additional SPA routes for better navigation
Route::get('/spa/cart', function () {
    return view('spa');
})->name('spa.cart');

Route::get('/spa/checkout', function () {
    return view('spa');
})->name('spa.checkout');

Route::get('/spa/payment-success', function () {
    return view('spa');
})->name('spa.payment-success');

Route::get('/spa/item/{item}', function () {
    return view('spa');
})->name('spa.item.detail');

// Test route for SPA debugging
Route::get('/spa-test', function () {
    return view('spa-test');
})->name('spa.test');

// Test API endpoints
Route::get('/test-api', function () {
    return response()->json([
        'message' => 'API is working',
        'timestamp' => now(),
        'branches' => \App\Models\Branch::count(),
        'items_endpoint' => url('/ajax-items'),
        'auction_types_endpoint' => url('/ajax-auction-types')
    ]);
});

// API route for adverts
Route::get('/api/adverts', function () {
    return response()->json(\App\Models\Advert::all());
});

// Session-based user endpoint for Vue.js authentication
Route::get('/api/user', function () {
    if (auth()->check()) {
        $user = auth()->user();
        $user->load('roles'); // Load roles if needed
        return response()->json($user);
    }
    return response()->json(null, 401);
})->name('api.session.user');

// Session-based API endpoints for Vue.js
Route::middleware('auth')->group(function () {
    // Watchlist endpoints
    Route::get('/api/watchlist', [App\Http\Controllers\Api\WatchlistController::class, 'index'])->name('api.session.watchlist.index');
    Route::post('/api/watchlist', [App\Http\Controllers\Api\WatchlistController::class, 'store'])->name('api.session.watchlist.store');
    Route::delete('/api/watchlist/{itemId}', [App\Http\Controllers\Api\WatchlistController::class, 'destroy'])->name('api.session.watchlist.destroy');
    Route::get('/api/watchlist/check/{itemId}', [App\Http\Controllers\Api\WatchlistController::class, 'check'])->name('api.session.watchlist.check');
    Route::get('/api/watchlist/count', [App\Http\Controllers\Api\WatchlistController::class, 'count'])->name('api.session.watchlist.count');
    Route::post('/api/watchlist/toggle', [App\Http\Controllers\Api\WatchlistController::class, 'toggle'])->name('api.session.watchlist.toggle');

    // Bid dashboard endpoint
    Route::get('/api/bid-dashboard', [App\Http\Controllers\Api\BidDashboardController::class, 'index'])->name('api.session.bid-dashboard');
});

// SPA Catch-all route - MUST BE LAST
// This handles all SPA routes that don't match other routes
Route::get('/spa/{any}', function () {
    return view('spa');
})->where('any', '.*')->name('spa.catchall');
