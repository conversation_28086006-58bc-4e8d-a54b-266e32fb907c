

    <!--=============================================
    =            footer area         =
    =============================================-->

    <div class="footer-container footer-one pt-50">
        <div class="container wide">
            <div class="row">
                <div class="col-md-5 footer-single-widget left-divider">
                    <!--=======  copyright text  =======-->
                    <!--=======  logo  =======-->

                    <div class="logo d-flex justify-content-center align-items-center">
                        <img src="<?php echo e(asset('assets/img/logo/logo_white_512x512.png')); ?>" alt="" height="200px">
                        <p class="h6 text-white pl-3">
                            We offer Auctioneering, Valution And <br> Estate Agency Services in Malawi. <br>We have Offices in Blantyre A
                        </p>
                    </div>

                    <!--=======  End of logo  =======-->

                    <!--=======  copyright text  =======-->

                   <!--  <div class="copyright-text">
                        <p> &copy; <?php echo e(date('Y')); ?>. <span>All Rights Reserved</span></p>
                    </div> -->

                    <!--=======  End of copyright text  =======-->

                    <!--=======  End of copyright text  =======-->
                </div>
                
                <div class="col-md-3 footer-single-widget left-divider ">
                    <!--=======  single widget  =======-->
                    <h5 class="h6 text-white mb-25">USEFUL LINKS</h5>

                    <!--=======  footer navigation container  =======-->

                    <div class="footer-nav-container">
                        <nav>
                            <ul>
                                <li><a href="#">Returns</a></li>
                                <li><a href="#">Support Policy</a></li>
                                <li><a href="#">Size guide</a></li>
                                <li><a href="#">FAQs</a></li>
                            </ul>
                        </nav>
                    </div>

                    <!--=======  End of footer navigation container  =======-->

                    <!--=======  single widget  =======-->
                </div>

                <div class="col-md-3 footer-single-widget">
                    <!--=======  single widget  =======-->
                    <h5 class="h6 text-white mb-25">Contacts</h5>

                    <!--=======  footer navigation container  =======-->

                    <div class="footer-nav-container footer-social-links">
                        <nav>
                            <ul>
                                <li class="text-white"><i class="bi bi-geo-alt icon-md"></i> Ali Hassan Mwinyi Road BT3 Blantyre, Malawi</li>
                                <li class="text-white"><i class="bi bi-telephone icon-md"></i> 01 871 144</li>
                                <li class="text-white"><i class="bi bi-envelope icon-md"></i> <EMAIL></li>
                            </ul>
                        </nav>
                    </div>

                    <!--=======  End of footer navigation container  =======-->


                    <!--=======  single widget  =======-->
                </div>
            </div>
            
        </div>
        <div class="top-divider mt-40">
            <h6 class="text-center text-white p-3">
                Copyright &copy; <?php echo e(date('Y')); ?> Trust Auctioneers and Estate Agents. All rights reserved.
            </h6>        
        </div>
    </div>

    <!--=====  End of footer area  ======-->

    <!--=============================================
    =            overlay items         =
    =============================================-->

    <!--=======  about overlay  =======-->

    <div class="header-offcanvas about-overlay" id="about-overlay">
        <div class="overlay-close inactive"></div>
        <div class="overlay-content">

            <!--=======  close icon  =======-->

            <span class="close-icon" id="about-close-icon">
                <a href="javascript:void(0)">
                    <i class="ti-close"></i>
                </a>
            </span>

            <!--=======  End of close icon  =======-->

            <!--=======  overlay content container  =======-->

            <div class="overlay-content-container d-flex flex-column justify-content-between h-100">
                <!--=======  widget wrapper  =======-->

                <div class="widget-wrapper">
                    <!--=======  single widget  =======-->

                    <div class="single-widget">
                        <h2 class="widget-title">About Us</h2>
                        <p><?php echo optional( App\Models\User::first())->getGlobal("about") ?? ''; ?></p>
                    </div>

                    <!--=======  End of single widget  =======-->
                </div>

                <!--=======  End of widget wrapper  =======-->

                <!--=======  contact widget  =======-->

                <div class="contact-widget">
                    <p class="email"><a href="mailto:<EMAIL>"><?php echo e(optional( App\Models\User::first())->getGlobal("email") ?? ''); ?></a></p>
                    <p class="phone"><?php echo e(optional( App\Models\User::first())->getGlobal("phone") ?? ''); ?></p>

                    <div class="social-icons">
                        <ul>
                            <li><a href="http://www.twitter.com/" data-tippy="Twitter" data-tippy-inertia="true"
                                    data-tippy-animation="shift-away" data-tippy-delay="50" data-tippy-arrow="true"
                                    data-tippy-theme="sharpborder" target="_blank"><i class="fa fa-twitter"></i></a></li>
                            <li><a href="http://www.facebook.com/" data-tippy="Facebook" data-tippy-inertia="true"
                                    data-tippy-animation="shift-away" data-tippy-delay="50" data-tippy-arrow="true"
                                    data-tippy-theme="sharpborder" target="_blank"><i class="fa fa-facebook"></i></a></li>
                            <li><a href="http://www.instagram.com/" data-tippy="Instagram" data-tippy-inertia="true"
                                    data-tippy-animation="shift-away" data-tippy-delay="50" data-tippy-arrow="true"
                                    data-tippy-theme="sharpborder" target="_blank"><i class="fa fa-instagram"></i></a></li>
                            <li><a href="http://www.youtube.com/" data-tippy="Youtube" data-tippy-inertia="true"
                                    data-tippy-animation="shift-away" data-tippy-delay="50" data-tippy-arrow="true"
                                    data-tippy-theme="sharpborder" target="_blank"><i class="fa fa-youtube-play"></i></a></li>
                        </ul>
                    </div>
                </div>

                <!--=======  End of contact widget  =======-->
            </div>

            <!--=======  End of overlay content container  =======-->
        </div>
    </div>

    <!--=======  End of about overlay  =======-->

    <!--=======  cart overlay  =======-->
    <?php if( auth()->check() ): ?>
    <div class="cart-overlay" id="cart-overlay">
        <div class="cart-overlay-close inactive"></div>
        <div class="cart-overlay-content">
            <!--=======  close icon  =======-->

            <span class="close-icon" id="cart-close-icon">
                <a href="javascript:void(0)">
                    <i class="ion-android-close"></i>
                </a>
            </span>

            <!--=======  End of close icon  =======-->

            <!--=======  offcanvas cart content container  =======-->

            <div class="offcanvas-cart-content-container">
                <h3 class="cart-title">Overview</h3>

                <div class="cart-product-wrapper">

                    <?php if(auth()->guard()->check()): ?>
                    <?php $lSale = Facades\App\Cache\Repo::myBidList() ?>
                    <?php if($lSale->count() ): ?>
                    <h3 class="cart-title">Live Auction Items</h3>
                    <div class="cart-product-container ps-scroll">
                        <!--=======  single cart product  =======-->
                        <?php $__currentLoopData = $lSale; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bid): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($bid->item): ?>
                            <div class="single-cart-product">
                                <span class="cart-close-icon">
                                    <a href="/cancel-bid/<?php echo e($bid->id); ?>">
                                        <i class="ti-close"></i>
                                    </a>
                                </span>
                                <div class="image">
                                    <a href="#">
                                        <img src="<?php echo e($bid->item->image ?? ''); ?>" class="img-fluid" alt="">
                                    </a>
                                </div>
                                <div class="content">
                                    <h5>
                                        <a href="#"> <?php echo e($bid->item->name ?? ''); ?> </a>
                                    </h5>
                                    <p>
                                        <span class="discounted-price"> <?php echo e(_money(  $bid->bid_amount  )); ?> </span>
                                    </p>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="single-cart-product">
                                <center>You have not yet bid on Item </center>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <!--=======  End of single cart product  =======-->
                    </div>                     
                    <!--=======  subtotal calculation  =======-->
                    <p class="cart-subtotal">
                        <span class="subtotal-title">Total Amount:</span>
                        <span class="subtotal-amount"><?php echo e(_money( $lSale->sum('bid_amount') )); ?></span>
                    </p>
                    <!--=======  End of subtotal calculation  =======-->
                    <?php endif; ?>
                    <?php endif; ?>



                    <?php $cSale = Facades\App\Cache\Repo::cart() ?>
                    <?php if($cSale->count() ): ?>
                    <h3 class="cart-title">Cash Sale</h3>
                    <div class="cart-product-container ps-scroll">
                        <!--=======  single cart product  =======-->
                        <?php $__currentLoopData = $cSale; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="single-cart-product">
                            <span class="cart-close-icon">
                                <a href="/remove-from-cart/<?php echo e($item->id); ?>">
                                    <i class="ti-close"></i>
                                </a>
                            </span>
                            <div class="image">
                                <a href="#">
                                    <img src="<?php echo e($item->image ?? ''); ?>" class="img-fluid" alt="">
                                </a>
                            </div>
                            <div class="content">
                                <h5>
                                    <a href="#"> <?php echo e($item->name ?? ''); ?> </a>
                                </h5>
                                <p>
                                    <span class="discounted-price"> <?php echo e(_money( $item->target_amount  )); ?> </span>
                                </p>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <!--=======  End of single cart product  =======-->
                    </div>
                    <!--=======  subtotal calculation  =======-->
                    <div class="cart-subtotal">
                        <span class="subtotal-title">Total Amount:</span>
                        <span class="subtotal-amount"><?php echo e(_money( $cSale->sum('target_amount') )); ?></span>
                    </div>
                    <!--=======  End of subtotal calculation  =======-->
                    <!--=======  cart buttons  =======-->
                    <div class="cart-buttons">
                        <a href="/checkout">Proceed To Chechout</a>
                    </div>
                    <!--=======  End of cart buttons  =======-->
                    <?php endif; ?>



                </div>
            </div>

            <!--=======  End of offcanvas cart content container   =======-->
        </div>
    </div>
    <?php endif; ?>

    <!--=======  End of cart overlay  =======-->


    <!--=======  search overlay  =======-->

    <div class="search-overlay" id="search-overlay">

        <!--=======  close icon  =======-->

        <span class="close-icon search-close-icon">
            <a href="javascript:void(0)" id="search-close-icon">
                <i class="ti-close"></i>
            </a>
        </span>

        <!--=======  End of close icon  =======-->

        <!--=======  search overlay content  =======-->

        <div class="search-overlay-content">
            <div class="input-box">
                <form action="">
                    <input type="search" name="search" placeholder="Search ...">
                </form>
            </div>
            <div class="search-hint">
                <span># Hit enter to search or ESC to close</span>
            </div>
        </div>

        <!--=======  End of search overlay content  =======-->
    </div>

    <!--=======  End of search overlay  =======-->

    <!--=====  End of overlay items  ======-->


    <!--=============================================
    =            quick view         =
    =============================================-->

    <div id="qv-1" class="cd-quick-view">
        <div class="cd-slider-wrapper" v-if="item">
            <ul class="cd-slider">
                <li class="selected">
                    <img :src="item?.image" class="img-v" alt="Product 2">
                </li>
                <li>
                    <img :src="item?.image" class="img-v" alt="Product 1">
                </li>
            </ul> <!-- cd-slider -->

            <ul class="cd-slider-pagination">
                <li class="active"><a href="#0">1</a></li>
                <li><a href="#1">2</a></li>
            </ul> <!-- cd-slider-pagination -->

            <ul class="cd-slider-navigation">
                <li><a class="cd-prev" href="#0">Prev</a></li>
                <li><a class="cd-next" href="#0">Next</a></li>
            </ul> <!-- cd-slider-navigation -->
        </div> <!-- cd-slider-wrapper -->

        <div id="bid-el" class="lezada-item-info cd-item-info ps-scroll" v-if="item">

            <h2 class="item-title" v-text="item.name"></h2>
            <p class="price">
                <span class="discounted-price">Current Bid: <span v-text="item.bid_amount"></span></span>
            </p>

            <p class="description" v-text="item.description" ></p>

            <span class="quickview-title">Amount:</span>
            <div class=" d-inline-block mb-40">
                <input type="number" :value="item.bid_amount">
            </div>

            <div class="add-to-cart-btn mb-25">
                <button class="lezada-button lezada-button--medium">Bid</button>
            </div>

           </div> <!-- cd-item-info -->
        <a href="#0" class="cd-close">Close</a>
    </div>

    <!--=====  End of quick view  ======-->

    <!-- scroll to top  -->
    <a href="#" class="scroll-top"></a>
    <!-- end of scroll to top -->


<?php $__env->startPush('scripts'); ?>
    
    <script type="text/javascript">
        
        new Vue({
            el: "#bid-el",

            data(){
                return {
                    item: null,
                    item_id: null,
                }
            },

            methods: {

                getItem() {
                    axios.get("/ajax-item/" + this.item_id).then( res => {
                        this.item = res.data;
                        $(".img-v").attr("src", this.item.image);
                        console.log(this.item);
                    });
                },

            },

            created(){
                $(".trigger-quick-view").click( (e) => {
                    this.item_id = $(e.target).attr("data-id");
                    // Navigate to Vue 3 item detail page instead of opening modal
                    window.location.href = '/item/' + this.item_id;
                });
            }
        });

    </script>
        
<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/frontend/footer.blade.php ENDPATH**/ ?>