<?php if (isset($component)) { $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58 = $component; } ?>
<?php $component = App\View\Components\LgModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lg-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\LgModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
	 <?php $__env->slot('class', null, []); ?> preview-item-modal <?php $__env->endSlot(); ?>
	 <?php $__env->slot('title', null, []); ?> Product Preview <?php $__env->endSlot(); ?>

	<div id="item-el">
		<div v-if="item">
			<div class="row">
				<!-- Left Column - Image Section -->
				<div class="col-md-6 mb-4">
					<!-- Main Image -->
					<div v-if="img" class="mb-3">
						<a :href="img" data-fslightbox="gallery">
							<img class="img-fluid border rounded" :src="img" alt="Product Image" style="width: 100%; object-fit: contain;">
						</a>
					</div>

					<!-- Thumbnail Gallery -->
					<div class="d-flex flex-wrap">
						<div v-for="(image, index) in item.media" :key="index" class="me-2 mb-2" style="width: 70px; height: 70px;">
							<img
								class="img-fluid rounded cursor-pointer"
								@click="img = image.original_url"
								:src="image.original_url"
								alt="Thumbnail"
								style="width: 100%; height: 100%; object-fit: cover; border: 2px solid"
								:class="{'border-primary': img === image.original_url}"
							>
						</div>
					</div>
				</div>

				<!-- Right Column - Product Details -->
				<div class="col-md-6">
					<form action="/place-a-bid/<?php echo e($item->id ?? ''); ?>" method="POST">
						<?php echo csrf_field(); ?>

						<!-- Product Title -->
						<h4 class="mb-3" v-text="item.name"></h4>

						<!-- Product Category -->
						<div class="d-flex mb-3">
							<span class="text-muted me-2">Category:</span>
							<span v-text="item?.auction_type?.name"></span>
						</div>

						<!-- Product Price -->
						<div class="mb-3">
							<div class="d-flex align-items-center">
								<span class="me-2">Amount:</span>
								<span class="h5 mb-0 fw-bold" v-text="formatNumber(item.target_amount)"></span>
							</div>
						</div>

						<hr class="my-3">

						<!-- Product Description -->
						<div class="mb-4">
							<p class="mb-0" v-text="item.description" style="max-height: 120px; overflow-y: auto;"></p>
						</div>

						<!-- Action Buttons -->
						<div class="d-flex gap-2 mb-3">
							<?php if(auth()->check()): ?>
								<div v-if="item?.auction_type?.type == 'cash'" class="d-flex gap-2 w-100">
									<a href="#" @click="viewItem" class="btn btn-primary flex-grow-1">PROCEED</a>
									<a :href="'/add-to-cart/' + item.id" class="btn btn-outline-primary flex-grow-1">ADD ITEM</a>
								</div>
								<div v-else class="w-100">
									<a href="#" @click="viewItem" class="btn btn-primary w-100">PROCEED</a>
								</div>
							<?php else: ?>
								<a href="#" @click="viewItem" class="btn btn-primary w-100">LOGIN TO PROCEED</a>
							<?php endif; ?>
						</div>

						<!-- Enhanced View Link -->
						<div class="text-center">
							<a :href="'/item/' + item.id" class="btn btn-outline-secondary btn-sm">
								<i class="bi bi-arrows-fullscreen me-1"></i>
								Enhanced View
							</a>
						</div>
					</form>
				</div>
			</div>
		</div>
		<div v-else class="text-center py-4">
			<div class="spinner-border text-primary" role="status">
				<span class="visually-hidden">Loading...</span>
			</div>
			<p class="mt-2">Loading product details...</p>
		</div>
	</div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58)): ?>
<?php $component = $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58; ?>
<?php unset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>

	<script type="text/javascript">
		var previewitem = new Vue({
			el: '#item-el',

			data(){
				return {
					item: null,
					img: null,
				}
			},

			methods: {
				setItem(item) {
					this.item = item;
					if(this.item.media.length > 0) {
						this.img = this.item.media[0].original_url;
					}
					console.log( this.item)
				},

				formatNumber (num) {
                  if( num > 0 ) {
                  } else {
                    return num;
                  }
                  return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
                },

                viewItem() {
                    if( this.item.auction_type.type == 'cash') {
                        window.location.href = '/view-cash-item/' + this.item.id;
                        return;
                    }
                    window.location.href = '/view-item/' + this.item.id;
                },


			},

			created() {

			}
		})
	</script>

<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/preview-item-modal.blade.php ENDPATH**/ ?>