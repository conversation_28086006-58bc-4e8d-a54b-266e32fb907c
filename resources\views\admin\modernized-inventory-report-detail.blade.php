@extends('layouts.modernized-admin')

@section('title', 'Item Details - Vertigo AMS')

@section('page-title', 'Item Details')
@section('page-subtitle', 'Detailed information about the inventory item.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('reports.inventory') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to Inventory Report</span>
        <span class="lg:hidden">Back</span>
    </a>
    <button onclick="window.print()" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
        </svg>
        <span class="hidden lg:inline">Print Details</span>
        <span class="lg:hidden">Print</span>
    </button>
</div>
@endsection

@section('content')
<!-- Item Information Card -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-full mr-4">
                <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-gray-900">{{ $item->name }}</h3>
                <p class="text-sm text-gray-600 mt-1">Item ID: #{{ $item->id }}</p>
            </div>
        </div>
    </div>

    <div class="p-6">
        <div class="flex flex-col lg:flex-row lg:space-x-8">
            <!-- Item Image -->
            <div class="flex-shrink-0 mb-6 lg:mb-0">
                @if($item->getFirstMediaUrl('images'))
                <img class="h-64 w-64 rounded-lg object-cover mx-auto lg:mx-0" src="{{ $item->getFirstMediaUrl('images') }}" alt="{{ $item->name }}">
                @else
                <div class="h-64 w-64 rounded-lg bg-gray-200 flex items-center justify-center mx-auto lg:mx-0">
                    <svg class="h-24 w-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                @endif
            </div>

            <!-- Item Details -->
            <div class="flex-1">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Item Name</label>
                                <div class="mt-1 text-sm text-gray-900">{{ $item->name }}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Item Code</label>
                                <div class="mt-1 text-sm text-gray-900">{{ $item->code ?? 'N/A' }}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Reference Number</label>
                                <div class="mt-1 text-sm text-gray-900">{{ $item->reference_number ?? 'N/A' }}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Status</label>
                                <div class="mt-1">
                                    @if($item->closed_by)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Sold
                                    </span>
                                    @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Available
                                    </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Pricing Information</h4>
                        <div class="space-y-3">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="text-sm font-medium text-blue-800">Bid Amount</div>
                                <div class="text-xl font-bold text-blue-900">{{ _money($item->bid_amount) }}</div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-sm font-medium text-green-800">Target Amount</div>
                                <div class="text-xl font-bold text-green-900">{{ _money($item->target_amount) }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                @if($item->description)
                <div class="mt-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Description</h4>
                    <p class="text-gray-600">{{ $item->description }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Auction Information -->
@if($item->auctionType)
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Auction Information</h3>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ $item->auctionType->name }}</h4>
                @if($item->auctionType->description)
                <p class="text-gray-600 mb-4">{{ $item->auctionType->description }}</p>
                @endif

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Auction Type</label>
                        <div class="mt-1 text-sm text-gray-900">{{ ucfirst($item->auctionType->type ?? 'N/A') }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Starting Bid</label>
                        <div class="mt-1 text-sm text-gray-900">{{ _money($item->auctionType->bid_amount) }}</div>
                    </div>
                </div>
            </div>

            @if($item->auctionType->date_from || $item->auctionType->date_to)
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Auction Period</h4>
                <div class="grid grid-cols-2 gap-4">
                    @if($item->auctionType->date_from)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Start Date</label>
                        <div class="mt-1 text-sm text-gray-900">{{ \Carbon\Carbon::parse($item->auctionType->date_from)->format('M d, Y H:i') }}</div>
                    </div>
                    @endif
                    @if($item->auctionType->date_to)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">End Date</label>
                        <div class="mt-1 text-sm text-gray-900">{{ \Carbon\Carbon::parse($item->auctionType->date_to)->format('M d, Y H:i') }}</div>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endif

<!-- Timeline Information -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Timeline</h3>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700">Date Added</label>
                <div class="mt-1 text-sm text-gray-900">{{ $item->created_at->format('M d, Y H:i') }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                <div class="mt-1 text-sm text-gray-900">{{ $item->updated_at->format('M d, Y H:i') }}</div>
            </div>
            @if($item->date_from)
            <div>
                <label class="block text-sm font-medium text-gray-700">Available From</label>
                <div class="mt-1 text-sm text-gray-900">{{ $item->date_from->format('M d, Y H:i') }}</div>
            </div>
            @endif
            @if($item->date_to)
            <div>
                <label class="block text-sm font-medium text-gray-700">Available Until</label>
                <div class="mt-1 text-sm text-gray-900">{{ $item->date_to->format('M d, Y H:i') }}</div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Staff Information -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Staff Information</h3>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Created By</h4>
                @if($item->createdBy)
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <svg class="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">{{ $item->createdBy->name }}</div>
                        <div class="text-sm text-gray-500">{{ $item->createdBy->email }}</div>
                    </div>
                </div>
                @else
                <div class="text-sm text-gray-500">No staff information available</div>
                @endif
            </div>

            @if($item->updatedBy)
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Last Updated By</h4>
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">{{ $item->updatedBy->name }}</div>
                        <div class="text-sm text-gray-500">{{ $item->updatedBy->email }}</div>
                    </div>
                </div>
            </div>
            @endif

            @if($item->closed_by)
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Sold By</h4>
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                            <svg class="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6M8 11H6a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2v-6a2 2 0 00-2-2h-2"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">Staff ID: {{ $item->closed_by }}</div>
                        <div class="text-sm text-gray-500">Item marked as sold</div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
