

<form action="/place-a-bid" method="POST">
	<?php echo csrf_field(); ?>
	<?php if (isset($component)) { $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58 = $component; } ?>
<?php $component = App\View\Components\LgModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lg-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\LgModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
		 <?php $__env->slot('class', null, []); ?> bid-modal <?php $__env->endSlot(); ?>
		 <?php $__env->slot('title', null, []); ?> Place A bid <?php $__env->endSlot(); ?>


	    <div class="" id="bid-modal-el">
	    	<input type="hidden" value="supplier-debtors" name="type" required>
	      <div class="row mb-4">


					<div class="col-sm-6 mb-4">
						<label>Amount:</label>
				    <input type="number" step="0.01" value="" class="form-control" name="amount_total" required>	
					</div>

					<div class="col-sm-12 mb-4">
						<label>Note:</label>
						<textarea name="description" class="form-control" placeholder="Add comment." required></textarea>
					</div>

	      </div>
	    </div>

		 <?php $__env->slot('footer', null, []); ?> 
			<button type="submit" class="btn btn-primary"> Save </button>
		 <?php $__env->endSlot(); ?>
	 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58)): ?>
<?php $component = $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58; ?>
<?php unset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58); ?>
<?php endif; ?>


</form>




<?php $__env->startPush('scripts'); ?>

  <script type="text/javascript">
    
    new Vue({
      el: "#bid-modal-el",
      data(){
        return{
          customer_id: null,
          customer: null,
        }
      },

      methods: {


      },


      created(){

      	// this.getProducts();
      }

    })

  </script>

<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/bid-modal.blade.php ENDPATH**/ ?>