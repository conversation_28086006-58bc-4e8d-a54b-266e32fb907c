<!--=============================================
=            Header offcanvas about         =
=============================================-->

<header class="header header-offcanvas-about header-sticky" style="z-index: 999;">

    <!--=======  header bottom  =======-->
    <div class="header-bottom pt-md-50 pb-md-40 pt-sm-40 pb-sm-40">
        <div class="container wide">

            <!--=======  header bottom container  =======-->

            <div class="header-bottom-container">

                <!--=======  logo with off canvas  =======-->

                <div class="d-flex header-left-container">

                    <!--=======  offcanvas about icon  =======-->

                    <!-- <div class="offcanvas-about-icon mr-20 d-none d-lg-block">
                        <a href="javascript:void(0)" id="offcanvas-about-icon">
                            <i class="ion-navicon"></i>
                        </a>
                    </div> -->

                    <!--=======  End of offcanvas about icon  =======-->

                    <!--=======  logo   =======-->

                    <div class="logo pt-2 pb-2">
                        <a href="/" class="d-flex align-items-center">
                            <img src="<?php echo e(asset('assets/svg/logo/Trust - Badge dark Outline on Light BG.svg')); ?>" alt="" height="80">
                            <div class="ml-2">
                                <span class="pl-2 h5"><?php echo e(env('APP_NAME') ?? 'TRUST AUCTIONEERS'); ?></span> <br>
                            </div>
                        </a>
                    </div>

                    <!--=======  End of logo   =======-->
                </div>

                <!--=======  End of logo with off canvas  =======-->

                <!--=======  header bottom navigation  =======-->

                <div class="header-bottom-navigation">
                    <div class="site-main-nav d-none d-lg-block">
                        <nav class="site-nav header-right-container center-menu">
                            <ul>
                                  
                                <?php if(auth()->guard()->check()): ?> 
                                <li class="">
                                    <a href="/register-bid">Register Bid</a>
                                </li>  
                                <?php endif; ?> 
                                <?php if( auth()->user()): ?>                           
                                <li class="">
                                    <a href="/home">Home</a>
                                </li>                             
                                <!-- <li class="">
                                    <form action="/logout" method="POST" id="logout"><?php echo csrf_field(); ?></form>
                                    <a href="#" onclick="logout.submit()">logout</a>
                                </li> -->
                                <?php else: ?>
                                <li class="">
                                    <a href="/">Home</a>
                                </li>  
                                <li class="">
                                    <a href="#" data-toggle="modal" data-target=".frontend-modal-login">
                                        Login
                                    </a>
                                </li>                                
                                <li class="">
                                    <a href="#" data-toggle="modal" data-target=".frontend-modal-register">
                                        Signup
                                    </a>
                                </li>
                                <?php endif; ?>

                                <li class="">
                                    <a href="/about">About</a>
                                </li>  
   
                            </ul>
                        </nav>
                    </div>
                </div>

                <!--=======  End of header bottom navigation  =======-->

                <!--=======  headeer right container  =======-->

                <div class="header-right-container">

                    <!--=======  header right icons  =======-->

                    <div class="header-right-icons d-flex justify-content-end align-items-center h-100">
                        <!--=======  single-icon  =======-->

                        <!-- <div class="single-icon search">
                            <a href="javascript:void(0)" id="search-icon">
                                <i class="ion-ios-search-strong"></i>
                            </a>
                        </div> -->

                        <!--=======  End of single-icon  =======-->
                        <?php if(!auth()->check() ): ?>
                        <!--=======  single-icon  =======-->
                        <div class="single-icon user-login">
                            <a href="/admin-login">
                                Portal
                            </a>
                        </div>
                        <!--=======  End of single-icon  =======-->
                        <?php endif; ?>

                        <?php if( auth()->user()): ?>  
                        <div class="single-icon user-login">
                            <form action="/logout" method="POST" id="logout"><?php echo csrf_field(); ?></form>
                            <a href="#" onclick="logout.submit()">logout</a>
                            <!-- <a href="/profile">
                                <i class="ion-android-person"></i>
                                <small class=""><?php echo e(auth()->user()->name ?? ''); ?></small>
                            </a> -->
                        </div>
                        <?php endif; ?>

                        <!--=======  single-icon  =======-->
                        <?php if( auth()->check() && ! auth()->user()->isSuperAdmin() ): ?>
                        <div class="single-icon user-login">
                            <a href="/profile">
                                <i class="ion-android-person"></i>
                                <small class=""><?php echo e(auth()->user()->name ?? ''); ?></small>
                            </a>
                        </div>
                        <?php endif; ?>
                        <!--=======  End of single-icon  =======-->

                        <!--=======  single-icon  =======-->
                        <div class="single-icon cart">
                            <a href="/cart" id="offcanvas-cart-icon">
                                <!-- <i class="ion-ios-cart"></i> -->
                                <i class="bi bi-cart4 icon-md"></i>
                                <span class="count p-1">
                                    <?php echo e(Facades\App\Cache\Repo::notifications()); ?>

                                </span>
                            </a>
                        </div>
                        <!--=======  End of single-icon  =======-->

                        

                    </div>
                    <!--=======  End of header right icons  =======-->

                </div>

                <!--=======  End of headeer right container  =======-->


            </div>

            <!--=======  End of header bottom container  =======-->

            <!-- Mobile Navigation Start Here -->

            <div class="site-mobile-navigation d-block d-lg-none">
                <div id="dl-menu" class="dl-menuwrapper site-mobile-nav">
                    <!--Site Mobile Menu Toggle Start-->
                    <button class="dl-trigger hamburger hamburger--spin">
                        <span class="hamburger-box">
                            <span class="hamburger-inner"></span>
                        </span>
                    </button>
                    <!--Site Mobile Menu Toggle End-->
                    <ul class="dl-menu dl-menu-toggle">
                        <li class="">
                            <a href="/about">About</a>
                        </li>     
                        <?php if( auth()->user()): ?>                           
                        <li class="">
                            <a href="/home">Home</a>
                        </li>
                        <?php else: ?>
                        <li class="">
                            <a href="#"  data-toggle="modal" data-target=".frontend-modal-login">
                                Login
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Mobile Navigation End Here -->

        </div>
    </div>

    <!--=======  End of header bottom  =======-->
</header>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/frontend/header.blade.php ENDPATH**/ ?>